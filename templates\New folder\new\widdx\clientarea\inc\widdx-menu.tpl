{*
 * WIDDX Client Area Menu Icons
 * Enhanced with Boxicons for better consistency and theme support
 *}
{assign var="menuIcons" value=[
    'dashboard' => 'bx bx-home-circle',
    'home' => 'bx bx-home',

    'services' => 'bx bx-server',
    'domains' => 'bx bx-globe',
    'ssl' => 'bx bx-lock-alt',
    'website' => 'bx bx-desktop',
    'database' => 'bx bx-data',
    'backup' => 'bx bx-cloud-upload',

    'billing' => 'bx bx-credit-card',
    'orders' => 'bx bx-shopping-bag',
    'store' => 'bx bx-store',
    'marketplace' => 'bx bx-basket',

    'support' => 'bx bx-support',
    'tickets' => 'bx bx-message-dots',
    'open ticket' => 'bx bx-plus-circle',

    'security' => 'bx bx-shield-alt-2',
    'website security' => 'bx bx-shield-quarter',

    'email' => 'bx bx-envelope',
    'settings' => 'bx bx-cog',
    'account' => 'bx bx-user-circle',
    'profile' => 'bx bx-user',
    'contacts' => 'bx bx-group',

    'reports' => 'bx bx-bar-chart-alt-2',
    'tools' => 'bx bx-wrench',
    'announcements' => 'bx bx-bullhorn',
    'knowledgebase' => 'bx bx-book-open',
    'downloads' => 'bx bx-download',
    'network' => 'bx bx-network-chart',
    'affiliates' => 'bx bx-group'
]}

{* Main Menu Container *}
{foreach $navbar as $item}
    <li class="menu-item {if $item->getClass()}active{/if}"
        menuItemName="{$item->getName()}"
        id="{$item->getId()}"
        data-menu-item="{$item->getName()}">

        <a {if $item->hasChildren()}
            class="menu-link menu-toggle{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'} rtl-toggle{/if}"
            href="javascript:void(0);"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
           {else}
            class="menu-link"
            href="{$item->getUri()}"
           {/if}
           {if $item->getAttribute('target')} target="{$item->getAttribute('target')}" {/if}
           {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}
            dir="rtl"
           {/if}
           title="{$item->getLabel()}"
           aria-label="{$item->getLabel()}">

            {* Menu Icon *}
            <i class="menu-icon {if $item->hasIcon()}{$item->getIcon()}{else}{assign var="itemName" value=$item->getName()|lower|replace:'_':' '}{if isset($menuIcons[$itemName])}{$menuIcons[$itemName]}{else}bx bx-circle{/if}{/if}"
               aria-hidden="true"></i>

            {* Menu Text *}
            <div class="menu-text">{$item->getLabel()}</div>

            {* Badge (if exists) *}
            {if $item->hasBadge()}
                <span class="badge badge-center rounded-pill bg-primary {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}me-auto{else}ms-auto{/if}"
                      aria-label="{$item->getBadge()} {lang key='notifications'}">
                    {$item->getBadge()}
                </span>
            {/if}
        </a>
        {* Submenu (if has children) *}
        {if $item->hasChildren()}
            <ul class="menu-sub"
                {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}
                 style="padding-right: 1.5rem; padding-left: 0px;" dir="rtl"
                {/if}>

                {foreach $item->getChildren() as $childItem}
                    {if $childItem->getClass() && in_array($childItem->getClass(), ['dropdown-divider', 'nav-divider'])}
                        <div class="dropdown-divider"></div>
                    {else}
                        <li class="menu-item {if $childItem->getClass()}active{/if}"
                            menuItemName="{$childItem->getName()}"
                            id="{$childItem->getId()}">

                            <a class="menu-link"
                               href="{$childItem->getUri()}"
                               {if $childItem->getAttribute('target')}
                                target="{$childItem->getAttribute('target')}"
                               {/if}
                               {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}
                                dir="rtl"
                               {/if}
                               title="{$childItem->getLabel()}"
                               aria-label="{$childItem->getLabel()}">

                                {* Submenu Icon *}
                                <i class="menu-icon {if $childItem->hasIcon()}{$childItem->getIcon()}{else}{assign var="itemName" value=$childItem->getName()|lower|replace:'_':' '}{if isset($menuIcons[$itemName])}{$menuIcons[$itemName]}{else}bx bx-right-arrow-alt{/if}{/if}"
                                   aria-hidden="true"></i>

                                {* Submenu Text *}
                                <div class="menu-text">{$childItem->getLabel()}</div>

                                {* Submenu Badge (if exists) *}
                                {if $childItem->hasBadge()}
                                    <span class="badge badge-center rounded-pill bg-primary {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}me-auto{else}ms-auto{/if}"
                                          aria-label="{$childItem->getBadge()} {lang key='notifications'}">
                                        {$childItem->getBadge()}
                                    </span>
                                {/if}
                            </a>
                        </li>
                    {/if}
                {/foreach}
            </ul>
        {/if}
    </li>
{/foreach}