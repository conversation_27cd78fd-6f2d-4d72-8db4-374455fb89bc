<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WHMCS WIDDX Theme - CSS Usage Example</title>
    
    <!-- Bootstrap CSS (Required) -->
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css">
    
    <!-- WIDDX Unified CSS (Main Styles) -->
    <link rel="stylesheet" href="widdx-unified.min.css">
    
    <!-- RTL Support (For Arabic/RTL sites only) -->
    <link rel="stylesheet" href="widdx-rtl.min.css">
    
    <!-- Optional Animation Libraries -->
    <link rel="stylesheet" href="animate.min.css">
    <link rel="stylesheet" href="owl.carousel.min.css">
    <link rel="stylesheet" href="owl.theme.default.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center text-primary">مثال على استخدام WHMCS WIDDX Theme</h1>
                <p class="text-center text-muted">تم تحسين وضغط جميع ملفات CSS في ملفين رئيسيين</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon-circle bg-primary-soft text-primary me-3">
                                <i class="fas fa-server"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1">الخدمات النشطة</h5>
                                <div class="stat-value text-primary">12</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon-circle bg-success-soft text-success me-3">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1">النطاقات</h5>
                                <div class="stat-value text-success">8</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon-circle bg-warning-soft text-warning me-3">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1">الفواتير</h5>
                                <div class="stat-value text-warning">3</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">الملفات المحسنة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">✅ الملفات الجديدة:</h6>
                                <ul class="list-unstyled">
                                    <li><code>widdx-unified.min.css</code> - الملف الرئيسي الموحد</li>
                                    <li><code>widdx-rtl.min.css</code> - دعم RTL</li>
                                    <li><code>README.md</code> - دليل الاستخدام</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">📦 الملفات المحتفظ بها:</h6>
                                <ul class="list-unstyled">
                                    <li><code>bootstrap/bootstrap.min.css</code></li>
                                    <li><code>animate.min.css</code></li>
                                    <li><code>owl.carousel.min.css</code></li>
                                    <li><code>owl.theme.default.min.css</code></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success" role="alert">
                    <h6 class="alert-heading">✅ تم التحسين بنجاح!</h6>
                    <p class="mb-0">تم دمج وضغط <strong>17+ ملف CSS</strong> في <strong>ملفين رئيسيين</strong> مما يحسن الأداء ويقلل عدد الطلبات.</p>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">كيفية الاستخدام</h5>
                    </div>
                    <div class="card-body">
                        <h6>للمواقع العادية (LTR):</h6>
                        <pre class="bg-light p-3 rounded"><code>&lt;!-- Bootstrap (مطلوب) --&gt;
&lt;link rel="stylesheet" href="bootstrap/bootstrap.min.css"&gt;

&lt;!-- الملف الرئيسي الموحد --&gt;
&lt;link rel="stylesheet" href="widdx-unified.min.css"&gt;</code></pre>
                        
                        <h6 class="mt-3">للمواقع العربية (RTL):</h6>
                        <pre class="bg-light p-3 rounded"><code>&lt;!-- Bootstrap (مطلوب) --&gt;
&lt;link rel="stylesheet" href="bootstrap/bootstrap.min.css"&gt;

&lt;!-- الملف الرئيسي الموحد --&gt;
&lt;link rel="stylesheet" href="widdx-unified.min.css"&gt;

&lt;!-- دعم RTL --&gt;
&lt;link rel="stylesheet" href="widdx-rtl.min.css"&gt;</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Theme Toggle Button -->
    <div class="ww-color-switch">
        <button class="btn btn-sm" onclick="toggleTheme()">
            <span class="ww-theme-light">🌙</span>
            <span class="ww-theme-dark">☀️</span>
        </button>
    </div>
    
    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
    </script>
</body>
</html>
