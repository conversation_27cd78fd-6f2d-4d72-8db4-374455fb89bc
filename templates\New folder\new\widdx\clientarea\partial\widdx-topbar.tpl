<!-- WIDDX Enhanced Topbar Navigation -->
<nav class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme shadow-sm"
     id="layout-navbar"
     role="navigation"
     aria-label="{lang key='navigation.main'}">

  <!-- <PERSON>u Toggle <PERSON> (Mobile Only) -->
  <div class="layout-menu-toggle navbar-nav align-items-xl-center {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}ms-3 ms-xl-0{else}me-3 me-xl-0{/if} d-xl-none">
    <button class="nav-item nav-link px-0 {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}ms-xl-4{else}me-xl-4{/if} btn-menu-toggle"
            type="button"
            aria-label="{lang key='navigation.toggleMenu'}"
            aria-expanded="false"
            aria-controls="layout-menu">
      <div class="menu-toggle-icon">
        <i class="bx bx-menu bx-sm" aria-hidden="true"></i>
      </div>
    </button>
  </div>

  <!-- Company Logo (Mobile Center) -->
  <div class="navbar-brand mx-auto d-xl-none">
    <img src="{$WEB_ROOT}/assets/img/logo.png"
         alt="{$companyname} Logo"
         height="30"
         class="logo-img"
         loading="lazy">
  </div>

  <!-- Layout Overlay for Mobile Menu -->
  <div class="layout-overlay" aria-hidden="true"></div>

  <!-- Navigation Right Side -->
  <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">

    <!-- Desktop Search -->
    <div class="navbar-search hide-on-mobile" role="search">
      {include file="$template/clientarea/widget/widdx-search.tpl"}
    </div>

    <!-- Mobile Search Toggle -->
    <div class="navbar-search hide-on-pc">
      <div class="main-nav-search">
        <button class="search-toggle-btn"
                id="main-nav-search-btn"
                type="button"
                aria-label="{lang key='search.toggle'}"
                data-bs-toggle="modal"
                data-bs-target="#searchModal">
          <i class="fas fa-search" aria-hidden="true"></i>
        </button>
      </div>
    </div>

    <!-- Navigation Items -->
    <ul class="navbar-nav flex-row align-items-center {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}me-auto{else}ms-auto{/if}">

      <!-- Language/Currency Selector -->
      {if $languagechangeenabled && count($locales) > 1 || $currencies}
      <li class="nav-item lh-1 {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}ms-3{else}me-3{/if}">
        <button type="button"
                class="btn nav-btn lang-selector"
                data-bs-toggle="modal"
                data-bs-target="#modalChooseLanguage"
                aria-label="{lang key='language.selector'}"
                title="{lang key='language.change'}">
          <div class="d-inline-block align-middle">
            <div class="iti-flag {if $activeLocale.countryCode === 'GB'}us{else}{$activeLocale.countryCode|lower}{/if}"
                 aria-hidden="true">
            </div>
          </div>
        </button>
      </li>
      {/if}

      <!-- Quick Actions -->
      <li class="nav-item topbar-icon hide-on-mobile">
        <button class="nav-link nav-btn"
                type="button"
                data-bs-toggle="modal"
                data-bs-target="#quickActionsModal"
                aria-label="{lang key='quickActions.toggle'}"
                title="{lang key='quickActions.title'}">
          <div class="nav-icon-circle">
            <i class="fas fa-layer-group" aria-hidden="true"></i>
          </div>
        </button>
      </li>

      <!-- Shopping Cart -->
      <li class="nav-item hide-on-mobile">
        {include file="$template/clientarea/widget/widdx-cart.tpl"}
      </li>

      <!-- Notifications -->
      <li class="nav-item hide-on-mobile">
        {include file="$template/clientarea/widget/widdx-notification.tpl"}
      </li>

      <!-- Theme Toggle -->
      <li class="nav-item hide-on-mobile">
        {include file="$template/clientarea/widget/widdx-theme-toggle.tpl"}
      </li>

      <!-- User Profile -->
      <li class="nav-item">
        {include file="$template/clientarea/widget/widdx-profile.tpl"}
      </li>
    </ul>
  </div>
</nav>

<!-- Include Additional Components -->
{include file="$template/clientarea/widget/widdx-quick.tpl"}
{include file="$template/clientarea/widget/mobile/widdx-search-icon.tpl"}

<!-- Load Topbar Styles -->
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/widdx-topbar.css">

<!-- Load Topbar JavaScript -->
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/widdx-topbar.js" defer></script>

