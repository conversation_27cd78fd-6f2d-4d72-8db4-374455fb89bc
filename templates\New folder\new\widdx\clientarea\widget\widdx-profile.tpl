<!-- User Profile Widget -->
<li class="nav-item navbar-dropdown dropdown-user dropdown">
    <button class="nav-link dropdown-toggle hide-arrow profile-toggle"
            type="button"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
            aria-label="{lang key='profile.menu'} - {$client.fullName}"
            title="{lang key='profile.title'}">
        <div class="avatar avatar-online">
            <img id="profile-img"
                 src="https://www.gravatar.com/avatar/{$client.email|md5}?s=200&d=mm"
                 alt="{$client.fullName} {lang key='profile.avatar'}"
                 class="w-px-40 h-auto rounded-circle"
                 loading="lazy">
        </div>
    </button>

    <ul class="dropdown-menu dropdown-menu-end profile-dropdown" role="menu">
        <!-- Profile Header -->
        <li role="none">
            <div class="dropdown-item profile-header" role="menuitem" tabindex="-1">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <div class="avatar avatar-online">
                            <img id="profile-img-dropdown"
                                 src="https://www.gravatar.com/avatar/{$client.email|md5}?s=200&d=mm"
                                 alt="{$client.fullName} {lang key='profile.avatar'}"
                                 class="w-px-40 h-auto rounded-circle"
                                 loading="lazy">
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <span class="fw-semibold d-block profile-name">{$client.fullName}</span>
                        <small class="text-muted profile-email">{$client.email}</small>
                    </div>
                </div>
            </div>
        </li>
        <li role="none"><hr class="dropdown-divider"></li>
        <!-- Account Management -->
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/clientarea.php?action=details"
               role="menuitem">
                <i class="fas fa-user me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.myDetails'}</span>
            </a>
        </li>
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/index.php?rp=/account/users"
               role="menuitem">
                <i class="fas fa-users me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.userManagement'}</span>
            </a>
        </li>
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/index.php?rp=/account/contacts"
               role="menuitem">
                <i class="fas fa-address-book me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.contacts'}</span>
            </a>
        </li>
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/clientarea.php?action=emails"
               role="menuitem">
                <i class="fas fa-envelope me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.emailHistory'}</span>
            </a>
        </li>

        <li role="none"><hr class="dropdown-divider"></li>

        <!-- Security Settings -->
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/index.php?rp=/user/password"
               role="menuitem">
                <i class="fas fa-lock me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.changePassword'}</span>
            </a>
        </li>
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/index.php?rp=/user/security"
               role="menuitem">
                <i class="fas fa-shield-alt me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.securitySettings'}</span>
            </a>
        </li>

        <li role="none"><hr class="dropdown-divider"></li>

        <!-- Account Actions -->
        <li role="none">
            <a class="dropdown-item profile-menu-item"
               href="{$WEB_ROOT}/index.php?rp=/user/accounts"
               role="menuitem">
                <i class="fas fa-exchange-alt me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.switchAccount'}</span>
            </a>
        </li>
        <li role="none">
            <a class="dropdown-item profile-menu-item logout-item"
               href="{$WEB_ROOT}/logout.php"
               role="menuitem">
                <i class="fas fa-sign-out-alt me-2 profile-icon" aria-hidden="true"></i>
                <span class="align-middle">{lang key='profile.logout'}</span>
            </a>
        </li>
    </ul>
</li>

<style>
/* Profile Widget Styles */
.profile-toggle {
  border: none;
  background: transparent;
  padding: 0;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-toggle:hover {
  transform: scale(1.05);
}

.profile-toggle:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

.avatar {
  position: relative;
  display: inline-block;
}

.avatar img {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(var(--bs-success-rgb), 0.8);
}

.avatar-online::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: var(--bs-success);
  border: 2px solid var(--bs-body-bg);
  border-radius: 50%;
  animation: onlinePulse 2s infinite;
}

@keyframes onlinePulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.profile-dropdown {
  min-width: 280px;
  border: 1px solid rgba(var(--bs-border-color-rgb), 0.15);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0;
}

.profile-header {
  padding: 1rem 1.25rem;
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 0.5rem 0.5rem 0 0;
  border: none !important;
}

.profile-header:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05) !important;
}

.profile-name {
  color: var(--bs-primary);
  font-weight: 600;
  font-size: 0.95rem;
}

.profile-email {
  color: var(--bs-secondary-color);
  font-size: 0.8rem;
}

.profile-menu-item {
  padding: 0.75rem 1.25rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--bs-body-color);
}

.profile-menu-item:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  color: var(--bs-primary);
  text-decoration: none;
}

.profile-icon {
  width: 18px;
  text-align: center;
  color: var(--bs-secondary-color);
  transition: color 0.2s ease;
}

.profile-menu-item:hover .profile-icon {
  color: var(--bs-primary);
}

.logout-item {
  border-radius: 0 0 0.5rem 0.5rem;
}

.logout-item:hover {
  background-color: rgba(var(--bs-danger-rgb), 0.05);
  color: var(--bs-danger);
}

.logout-item:hover .profile-icon {
  color: var(--bs-danger);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .profile-dropdown {
  background-color: var(--bs-dark);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .profile-header {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

[data-bs-theme="dark"] .profile-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .logout-item:hover {
  background-color: rgba(var(--bs-danger-rgb), 0.1);
}

[data-bs-theme="dark"] .avatar img {
  border-color: rgba(var(--bs-success-rgb), 0.9);
}

[data-bs-theme="dark"] .avatar-online::after {
  border-color: var(--bs-dark);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .profile-toggle,
  .avatar img,
  .profile-menu-item,
  .profile-icon {
    transition: none;
  }

  .avatar-online::after {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .profile-dropdown {
    border: 2px solid var(--bs-primary);
  }

  .profile-toggle:focus {
    outline: 3px solid var(--bs-primary);
    outline-offset: 3px;
  }
}
</style>