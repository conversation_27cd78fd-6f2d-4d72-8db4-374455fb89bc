<style>
    .collapsable-card-body {
        display: block;
        /* جعل المحتوى مفتوحًا بشكل افتراضي */
    }

    .card-header .card-minimise {
        cursor: pointer;
    }

    .card-header i {
        transition: transform 0.3s ease;
    }

    .card-header i.rotate {
        transform: rotate(180deg);
    }

    .card-footer {
        background-color: #f8f9fa;
    }

    .mobile-select-card {
        display: none;
    }

    @media (max-width: 768px) {
        .mobile-select-card {
            display: block;
        }
    }
</style>

<!-- Sidebar script initialization -->
{assign var="menuIcons" value=[
    'hosting' => 'fas fa-server',
    'domains' => 'fas fa-globe',
    'billing' => 'fas fa-file-invoice-dollar',
    'support' => 'fas fa-headset',
    'tickets' => 'fas fa-ticket-alt',
    'store' => 'fas fa-shopping-cart',
    'security' => 'fas fa-shield-alt',
    'email' => 'fas fa-envelope',
    'ssl' => 'fas fa-lock',
    'dns' => 'fas fa-network-wired',
    'website' => 'fas fa-desktop',
    'database' => 'fas fa-database',
    'backup' => 'fas fa-backup',
    'settings' => 'fas fa-cog',
    'user' => 'fas fa-user',
    'affiliate' => 'fas fa-users',
    'reports' => 'fas fa-chart-bar',
    'tools' => 'fas fa-tools',
    'home' => 'fas fa-home',
    'dashboard' => 'fas fa-tachometer-alt',
    'announcements' => 'fas fa-bullhorn',
    'knowledgebase' => 'fas fa-book',
    'downloads' => 'fas fa-download',
    'network' => 'fas fa-network-wired',
    'default' => 'fas fa-angle-right'
]}

{foreach $sidebar as $item}
    <div menuItemName="{$item->getName()}"
        class="mb-3 card card-sidebar{if $item->getClass()} {$item->getClass()}{/if}{if $item->getExtra('mobileSelect') and $item->hasChildren()} d-none d-md-block{/if}"
        {if $item->getAttribute('id')} id="{$item->getAttribute('id')}" {/if}>
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title m-0">
                {if $item->hasIcon()}<i class="{$item->getIcon()}"></i>{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}&nbsp;{else}&nbsp;{/if}{/if}
                {$item->getLabel()}
                {if $item->hasBadge()}{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}&nbsp;<span class="badge badge-primary float-left">{$item->getBadge()}</span>{else}&nbsp;<span class="badge badge-primary float-right">{$item->getBadge()}</span>{/if}{/if}
            </h3>
            {if $item->hasChildren()}
                <i class="fas fa-chevron-up card-minimise"></i>
            {/if}
        </div>
        <div class="collapsable-card-body">
            {if $item->hasBodyHtml()}
                <div class="card-body">
                    {$item->getBodyHtml()}
                </div>
            {/if}
            {if $item->hasChildren()}
                <div class="list-group list-group-flush d-md-flex{if $item->getChildrenAttribute('class')} {$item->getChildrenAttribute('class')}{/if}"
                    role="tablist">
                    {foreach $item->getChildren() as $childItem}
                        {if $childItem->getUri()}
                            <a menuItemName="{$childItem->getName()}" href="{$childItem->getUri()}"
                                class="list-group-item list-group-item-action{if $childItem->isDisabled()} disabled{/if}{if $childItem->getClass()} {$childItem->getClass()}{/if}{if $childItem->isCurrent()} active{/if}"
                                {if $childItem->getAttribute('dataToggleTab')} data-toggle="list" role="tab" {/if}
                                {assign "customActionData" $childItem->getAttribute('dataCustomAction')}
                                {if is_array($customActionData)} data-active="{$customActionData['active']}"
                                    data-identifier="{$customActionData['identifier']}" data-serviceid="{$customActionData['serviceid']}"
                                    {/if} {if $childItem->getAttribute('target')} target="{$childItem->getAttribute('target')}" {/if}
                                    id="{$childItem->getId()}">
                                    <div class="d-flex align-items-center">
                                        <div class="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}ml-2{else}mr-2{/if}">
                                            {if $childItem->hasIcon()}
                                                <i class="{$childItem->getIcon()}"></i>
                                            {else}
                                                {assign var="itemName" value=$childItem->getName()|lower}
                                                {assign var="itemIcon" value=$menuIcons[$itemName]|default:$menuIcons.default}
                                                <i class="{$itemIcon}"></i>
                                            {/if}
                                        </div>
                                        <div class="flex-grow-1">
                                            {$childItem->getLabel()}
                                            {if $childItem->hasBadge()}<span
                                                class="badge badge-secondary">{$childItem->getBadge()}</span>{/if}
                                        </div>
                                        {if strpos($childItem->getClass(), 'menu-link menu-toggle') !== false}
                                            <div class="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}mr-2{else}ml-2{/if}">
                                                <i class="fas {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}fa-chevron-left{else}fa-chevron-right{/if} menu-arrow"></i>
                                            </div>
                                        {/if}
                                    </div>
                                </a>
                            {else}
                                <div menuItemName="{$childItem->getName()}"
                                    class="list-group-item list-group-item-action{if $childItem->getClass()} {$childItem->getClass()}{/if}"
                                    id="{$childItem->getId()}">
                                    <div class="d-flex align-items-center">
                                        <div class="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}ml-2{else}mr-2{/if}">
                                            {if $childItem->hasIcon()}
                                                <i class="{$childItem->getIcon()}"></i>
                                            {else}
                                                {assign var="itemName" value=$childItem->getName()|lower}
                                                {assign var="itemIcon" value=$menuIcons[$itemName]|default:$menuIcons.default}
                                                <i class="{$itemIcon}"></i>
                                            {/if}
                                        </div>
                                        <div class="flex-grow-1">
                                            {$childItem->getLabel()}
                                            {if $childItem->hasBadge()}<span
                                                class="badge badge-secondary">{$childItem->getBadge()}</span>{/if}
                                        </div>
                                        {if strpos($childItem->getClass(), 'menu-link menu-toggle') !== false}
                                            <div class="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}mr-2{else}ml-2{/if}">
                                                <i class="fas {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}fa-chevron-left{else}fa-chevron-right{/if} menu-arrow"></i>
                                            </div>
                                        {/if}
                                    </div>
                                </div>
                            {/if}
                        {/foreach}
                    </div>
                {/if}
            </div>
            {if $item->hasFooterHtml()}
                <div class="card-footer clearfix">
                    {$item->getFooterHtml()}
                </div>
            {/if}
        </div>

        {if $item->getExtra('mobileSelect') and $item->hasChildren()}
            <div class="card mobile-select-card">
                <div class="card-header">
                    <h3 class="card-title">
                        {if $item->hasIcon()}<i class="{$item->getIcon()}"></i>{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}&nbsp;{else}&nbsp;{/if}{/if}
                        {$item->getLabel()}
                        {if $item->hasBadge()}{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}&nbsp;<span class="badge badge-primary float-left">{$item->getBadge()}</span>{else}&nbsp;<span class="badge badge-primary float-right">{$item->getBadge()}</span>{/if}{/if}
                    </h3>
                </div>
                <div class="card-body">
                    <form role="form">
                        <select class="form-control" onchange="selectChangeNavigate(this)">
                            {foreach $item->getChildren() as $childItem}
                                <option menuItemName="{$childItem->getName()}" value="{$childItem->getUri()}"
                                    {if $childItem->isCurrent()}selected="selected" {/if}>
                                    {$childItem->getLabel()}
                                    {if $childItem->hasBadge()}({$childItem->getBadge()}){/if}
                                </option>
                            {/foreach}
                        </select>
                    </form>
                </div>
                {if $item->hasFooterHtml()}
                    <div class="card-footer">
                        {$item->getFooterHtml()}
                    </div>
                {/if}
            </div>
        {/if}
    {/foreach}

<style>
    .menu-arrow {
        transition: transform 0.3s ease;
    }

    {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}
    .menu-link.menu-toggle.active .menu-arrow {
        transform: rotate(-90deg);
    }
    {else}
    .menu-link.menu-toggle.active .menu-arrow {
        transform: rotate(90deg);
    }
    {/if}
</style>

<script>
    jQuery(document).ready(function($) {
        $('.menu-link.menu-toggle').on('click', function(e) {
            e.preventDefault();
            $(this).toggleClass('active');
            $(this).find('.menu-arrow').toggleClass('rotate');
        });
    });
</script>
