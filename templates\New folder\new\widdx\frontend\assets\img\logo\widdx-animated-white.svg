<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200" preserveAspectRatio="xMidYMid meet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@700&amp;display=swap');
    
    .text {
      font-family: 'Cairo', sans-serif;
      font-size: 120px;
      font-weight: 700;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .text-stroke {
      fill: none;
      stroke: #fff;
      stroke-width: 3;
    }
    .text-fill {
      fill: #fff;
    }
    @keyframes animate {
      0%, 100% {
        clip-path: polygon(
          0% 45%, 16% 44%, 33% 50%, 54% 60%, 70% 61%,
          84% 59%, 100% 52%, 100% 100%, 0% 100%
        );
      }
      50% {
        clip-path: polygon(
          0% 60%, 15% 65%, 34% 66%, 51% 62%, 67% 50%,
          84% 45%, 100% 46%, 100% 100%, 0% 100%
        );
      }
    }
    .text-fill {
      animation: animate 4s ease-in-out infinite;
    }
  </style>
  <text x="50%" y="50%" class="text text-stroke">WIDDX</text>
  <text x="50%" y="50%" class="text text-fill">WIDDX</text>
</svg>
