<!-- Desktop Search Widget -->
<div class="navbar-nav align-items-center">
  <div class="nav-item d-flex align-items-center">
    <form method="post"
          action="{routePath('knowledgebase-search')}"
          class="d-flex search-form"
          role="search">
      <div class="search-input-group">
        <input type="text"
               name="search"
               class="form-control search-input border-0 shadow-none"
               placeholder="{lang key='searchOurKnowledgebase'}..."
               aria-label="{lang key='search.placeholder'}"
               autocomplete="off"
               maxlength="100" />
        <button type="submit"
                class="btn btn-search"
                aria-label="{lang key='search.submit'}"
                title="{lang key='search.title'}">
          <i class="bx bx-search fs-4 lh-0" aria-hidden="true"></i>
        </button>
      </div>
    </form>
  </div>
</div>

<style>
/* Desktop Search Styles */
.search-form {
  position: relative;
  width: 280px;
  max-width: 100%;
}

.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgba(var(--bs-body-bg-rgb), 0.8);
  border: 1px solid rgba(var(--bs-border-color-rgb), 0.3);
  border-radius: 2rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input-group:hover,
.search-input-group:focus-within {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  background-color: var(--bs-body-bg);
}

.search-input {
  flex: 1;
  background: transparent !important;
  border: none !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem;
  color: var(--bs-body-color);
}

.search-input:focus {
  outline: none;
  box-shadow: none !important;
}

.search-input::placeholder {
  color: var(--bs-secondary-color);
  opacity: 0.7;
}

.btn-search {
  background: transparent;
  border: none;
  padding: 0.5rem;
  color: var(--bs-secondary-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-search:hover,
.btn-search:focus {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  transform: scale(1.05);
}

.btn-search:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* Dark Mode Adjustments */
[data-bs-theme="dark"] .search-input-group {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(51, 65, 85, 0.5);
}

[data-bs-theme="dark"] .search-input-group:hover,
[data-bs-theme="dark"] .search-input-group:focus-within {
  background-color: rgba(30, 41, 59, 1);
  border-color: var(--bs-primary);
}

[data-bs-theme="dark"] .search-input {
  color: var(--bs-gray-100);
}

[data-bs-theme="dark"] .search-input::placeholder {
  color: var(--bs-gray-400);
}

/* Responsive adjustments */
@media (max-width: 1399.98px) {
  .search-form {
    width: 240px;
  }
}

@media (max-width: 1199.98px) {
  .search-form {
    width: 200px;
  }
}
</style>