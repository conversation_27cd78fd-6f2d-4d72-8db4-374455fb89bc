
<!-- Shopping Cart Widget -->
<a class="nav-link nav-btn cart-link"
   href="{$WEB_ROOT}/cart.php?a=view"
   aria-label="{lang key='cart.viewItems'} ({if $cartitemcount > 0}{$cartitemcount} {lang key='cart.items'}{else}{lang key='cart.empty'}{/if})"
   title="{lang key='cart.title'}">
    <div class="nav-icon-circle">
        <i class="fas fa-shopping-cart" aria-hidden="true"></i>
        {if $cartitemcount > 0}
            <span id="cartItemCount"
                  class="badge bg-danger badge-number"
                  aria-label="{$cartitemcount} {lang key='cart.items'}">
                {$cartitemcount}
            </span>
        {/if}
    </div>
</a>

<style>
/* Cart Widget Styles */
.cart-link {
  position: relative;
}

.cart-link:hover {
  text-decoration: none;
}

.cart-link .nav-icon-circle {
  position: relative;
}

.cart-link .badge-number {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--bs-body-bg);
  animation: cartBadgePulse 2s infinite;
}

/* Cart badge animation */
@keyframes cartBadgePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Disable animation for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  .cart-link .badge-number {
    animation: none;
  }
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .cart-link .badge-number {
  border-color: var(--bs-dark);
}

/* Cart icon hover effect */
.cart-link:hover .fas.fa-shopping-cart {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Focus styles for accessibility */
.cart-link:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
  border-radius: 50%;
}
</style>
