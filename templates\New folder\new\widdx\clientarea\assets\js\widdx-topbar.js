/*!
 * WIDDX Topbar JavaScript
 * Enhanced navigation functionality with accessibility support
 * Copyright (c) 2024 WIDDX Template
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initTopbarFunctionality();
    });

    function initTopbarFunctionality() {
        // Initialize menu toggle
        initMenuToggle();
        
        // Initialize search functionality
        initSearchFunctionality();
        
        // Initialize theme toggle
        initThemeToggle();
        
        // Initialize notifications
        initNotifications();
        
        // Initialize accessibility features
        initAccessibility();
        
        // Initialize responsive features
        initResponsiveFeatures();
    }

    // Menu Toggle Functionality
    function initMenuToggle() {
        const menuToggle = document.querySelector('.btn-menu-toggle');
        const layoutMenu = document.getElementById('layout-menu');
        const layoutOverlay = document.querySelector('.layout-overlay');

        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                
                // Toggle aria-expanded
                this.setAttribute('aria-expanded', !isExpanded);
                
                // Toggle menu visibility
                if (layoutMenu) {
                    layoutMenu.classList.toggle('show');
                }
                
                // Toggle overlay
                if (layoutOverlay) {
                    layoutOverlay.classList.toggle('show');
                }
                
                // Add animation class
                this.classList.add('menu-toggle-active');
                setTimeout(() => {
                    this.classList.remove('menu-toggle-active');
                }, 300);
            });
        }

        // Close menu when clicking overlay
        if (layoutOverlay) {
            layoutOverlay.addEventListener('click', function() {
                if (menuToggle) {
                    menuToggle.click();
                }
            });
        }
    }

    // Search Functionality
    function initSearchFunctionality() {
        const searchToggle = document.getElementById('main-nav-search-btn');
        const searchModal = document.getElementById('searchModal');
        const searchInput = searchModal?.querySelector('.search-input-mobile');

        if (searchToggle && searchModal) {
            // Focus search input when modal opens
            searchModal.addEventListener('shown.bs.modal', function() {
                if (searchInput) {
                    searchInput.focus();
                }
            });

            // Handle escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && searchModal.classList.contains('show')) {
                    const modal = bootstrap.Modal.getInstance(searchModal);
                    if (modal) {
                        modal.hide();
                    }
                }
            });
        }

        // Desktop search enhancements
        const desktopSearchInput = document.querySelector('.search-input');
        if (desktopSearchInput) {
            // Add search suggestions functionality here if needed
            desktopSearchInput.addEventListener('focus', function() {
                this.parentElement.classList.add('search-focused');
            });

            desktopSearchInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('search-focused');
            });
        }
    }

    // Theme Toggle Functionality
    function initThemeToggle() {
        const themeToggle = document.querySelector('.ww-theme-toggle');
        
        if (themeToggle) {
            themeToggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const html = document.documentElement;
                const currentTheme = html.getAttribute('data-bs-theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                // Set new theme
                html.setAttribute('data-bs-theme', newTheme);
                document.body.setAttribute('data-bs-theme', newTheme);
                
                // Save to localStorage
                localStorage.setItem('theme', newTheme);
                
                // Update button title
                const lightIcon = this.querySelector('.ww-theme-light');
                const darkIcon = this.querySelector('.ww-theme-dark');
                
                if (lightIcon && darkIcon) {
                    if (newTheme === 'dark') {
                        lightIcon.setAttribute('data-title', 'Light');
                        darkIcon.setAttribute('data-title', 'Dark');
                        this.setAttribute('title', 'Switch to Light Mode');
                    } else {
                        lightIcon.setAttribute('data-title', 'Dark');
                        darkIcon.setAttribute('data-title', 'Light');
                        this.setAttribute('title', 'Switch to Dark Mode');
                    }
                }
                
                // Dispatch custom event
                const event = new CustomEvent('themeChanged', {
                    detail: { theme: newTheme }
                });
                document.dispatchEvent(event);
                
                // Add animation class
                this.classList.add('theme-toggle-active');
                setTimeout(() => {
                    this.classList.remove('theme-toggle-active');
                }, 300);
            });
        }
    }

    // Notifications Functionality
    function initNotifications() {
        const notificationToggle = document.getElementById('notifDropdown');
        const notificationBadge = document.querySelector('.notification-badge');
        
        if (notificationToggle) {
            // Mark notifications as read when dropdown is opened
            notificationToggle.addEventListener('shown.bs.dropdown', function() {
                // Add logic to mark notifications as read
                if (notificationBadge) {
                    setTimeout(() => {
                        notificationBadge.style.opacity = '0.7';
                    }, 2000);
                }
            });
        }
    }

    // Accessibility Features
    function initAccessibility() {
        // Add keyboard navigation support
        const navButtons = document.querySelectorAll('.nav-btn, .search-toggle-btn, .lang-selector');
        
        navButtons.forEach(button => {
            button.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Add focus management for dropdowns
        const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const dropdown = this.nextElementSibling;
                    if (dropdown) {
                        const firstItem = dropdown.querySelector('.dropdown-item');
                        if (firstItem) {
                            firstItem.focus();
                        }
                    }
                }
            });
        });
    }

    // Responsive Features
    function initResponsiveFeatures() {
        // Handle window resize
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                handleResponsiveChanges();
            }, 250);
        });

        function handleResponsiveChanges() {
            const isMobile = window.innerWidth < 1200;
            const navbar = document.querySelector('.layout-navbar');
            
            if (navbar) {
                if (isMobile) {
                    navbar.classList.add('mobile-layout');
                } else {
                    navbar.classList.remove('mobile-layout');
                }
            }
        }

        // Initial check
        handleResponsiveChanges();
    }

    // Utility function to show toast notifications
    function showToast(message, type = 'info') {
        // Create toast element if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            toast.remove();
        });
    }

    // Export utility functions for global use
    window.WiddxTopbar = {
        showToast: showToast
    };

})();
