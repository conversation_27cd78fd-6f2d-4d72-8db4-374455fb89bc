# Lahza Payment Gateway - Security Audit Report

## 🔒 **Executive Summary**

**Audit Date**: 2025-01-21  
**Auditor**: WIDDX Security Team  
**Gateway Version**: 1.1.0  
**Overall Security Rating**: ⚠️ **MEDIUM-HIGH** (Requires Immediate Attention)

### 🎯 **Key Findings**
- **Critical Issues**: 2
- **High Priority Issues**: 3  
- **Medium Priority Issues**: 4
- **Low Priority Issues**: 2

---

## 🚨 **Critical Security Issues**

### 1. **Inconsistent IP Whitelisting Implementation**
**Severity**: CRITICAL  
**Risk Level**: HIGH  
**CVSS Score**: 7.5

**Issue**: IP whitelisting is defined in configuration but not consistently enforced across all webhook endpoints.

**Evidence**:
- `config.php` defines `WEBHOOK_IPS` array with Lahza IPs
- Main callback (`lahza.php`) does NOT implement IP validation
- 3DS callback (`lahza_3ds.php`) does NOT implement IP validation
- Only helper function exists but is not called

**Impact**: 
- Unauthorized webhook calls possible
- Potential payment manipulation
- Bypass of signature verification

**Recommendation**:
```php
// Add to all callback files
function validateWebhookIP() {
    $clientIP = $_SERVER['REMOTE_ADDR'];
    $allowedIPs = ['*************', '**************'];
    
    if (!in_array($clientIP, $allowedIPs)) {
        http_response_code(403);
        die('Unauthorized IP address');
    }
}
```

### 2. **Currency Conversion Logic Vulnerability**
**Severity**: CRITICAL  
**Risk Level**: HIGH  
**CVSS Score**: 8.2

**Issue**: Incorrect currency conversion for JOD (Jordanian Dinar) could lead to payment amount discrepancies.

**Evidence**:
```php
// In lahza.php line 862-864 - INCORRECT
$amountInSmallestUnit = $params['currency'] === 'USD' 
    ? (int)($params['amount'] * 100)  // USD: cents
    : (int)$params['amount'];         // WRONG for JOD
```

**Impact**:
- JOD payments processed with wrong amounts
- Financial losses or overcharges
- Compliance violations

**Recommendation**:
```php
// Correct implementation
switch ($params['currency']) {
    case 'USD': return (int)($amount * 100);    // cents
    case 'ILS': return (int)($amount * 100);    // agorot  
    case 'JOD': return (int)($amount * 1000);   // fils
    default: throw new Exception('Unsupported currency');
}
```

---

## ⚠️ **High Priority Security Issues**

### 3. **API Endpoint Inconsistencies**
**Severity**: HIGH  
**Risk Level**: MEDIUM-HIGH

**Issue**: Multiple different API endpoints used across files, creating confusion and potential security gaps.

**Evidence**:
- `lahza.php`: `https://api.lahza.io/transaction/initialize`
- `lahza/lahza.php`: `https://api.lahza.io/v1/payments`
- Inconsistent authentication patterns

### 4. **Insufficient Input Validation**
**Severity**: HIGH  
**Risk Level**: MEDIUM-HIGH

**Issue**: While basic validation exists, some edge cases are not covered.

**Gaps Identified**:
- Metadata fields not validated
- Phone number format not checked
- Special characters in names not sanitized
- Amount precision not validated for currency

### 5. **Webhook Replay Attack Vulnerability**
**Severity**: HIGH  
**Risk Level**: MEDIUM

**Issue**: No timestamp validation or nonce checking to prevent replay attacks.

**Recommendation**: Implement timestamp validation with 5-minute window.

---

## 🔧 **Medium Priority Issues**

### 6. **Error Message Information Disclosure**
**Severity**: MEDIUM  
**Risk Level**: MEDIUM

**Issue**: Some error messages may expose sensitive system information.

**Examples**:
- Database connection errors with full paths
- API key validation errors with key fragments
- Stack traces in production logs

### 7. **Session Management**
**Severity**: MEDIUM  
**Risk Level**: MEDIUM

**Issue**: Session handling in callback processing could be improved.

### 8. **Rate Limiting Bypass**
**Severity**: MEDIUM  
**Risk Level**: LOW-MEDIUM

**Issue**: Rate limiting uses IP-based identification which can be bypassed with proxies.

### 9. **Log File Security**
**Severity**: MEDIUM  
**Risk Level**: LOW-MEDIUM

**Issue**: Log files contain sensitive data and may not be properly protected.

---

## ✅ **Security Strengths**

### **Well-Implemented Security Features**:

1. **Webhook Signature Verification**
   - HMAC-SHA256 implementation
   - Timing-safe comparison using `hash_equals()`
   - Proper signature header validation

2. **SQL Injection Prevention**
   - Consistent use of prepared statements
   - PDO with proper parameter binding
   - No direct SQL concatenation found

3. **Input Validation Framework**
   - Comprehensive validation functions
   - Type checking and sanitization
   - Required field validation

4. **Secure Database Handling**
   - PDO with proper configuration
   - Transaction locking for critical operations
   - Connection retry logic with exponential backoff

5. **Logging Security**
   - Sensitive data redaction
   - Structured logging with context
   - Log rotation implementation

6. **3D Secure Implementation**
   - Proper challenge handling
   - Secure iframe implementation
   - Timeout management

---

## 🛠️ **Immediate Action Items**

### **Critical (Fix Before Go-Live)**:
1. ✅ Implement IP whitelisting in all callback files
2. ✅ Fix JOD currency conversion logic
3. ✅ Standardize API endpoints across all files

### **High Priority (Fix Within 48 Hours)**:
1. ⚠️ Add webhook replay attack prevention
2. ⚠️ Enhance input validation for edge cases
3. ⚠️ Sanitize error messages for production

### **Medium Priority (Fix Within 1 Week)**:
1. 🔧 Implement proper session management
2. 🔧 Enhance rate limiting with user-based tracking
3. 🔧 Secure log file access with proper permissions

---

## 📋 **Security Testing Recommendations**

### **Penetration Testing Scenarios**:
1. Webhook signature bypass attempts
2. SQL injection testing on all inputs
3. XSS testing on payment forms
4. Rate limiting bypass testing
5. Currency manipulation testing

### **Automated Security Scanning**:
1. OWASP ZAP scan of payment endpoints
2. Static code analysis with SonarQube
3. Dependency vulnerability scanning
4. SSL/TLS configuration testing

---

## 🎯 **Compliance Assessment**

### **PCI DSS Compliance Status**:
- ✅ No card data storage
- ✅ Secure transmission (HTTPS)
- ✅ Access control implementation
- ⚠️ Logging and monitoring (needs enhancement)
- ⚠️ Regular security testing (needs implementation)

### **GDPR Compliance**:
- ✅ Data minimization
- ✅ Secure processing
- ⚠️ Data retention policies (needs documentation)

---

## 📊 **Risk Assessment Matrix**

| Issue | Likelihood | Impact | Risk Level | Priority |
|-------|------------|--------|------------|----------|
| IP Whitelisting Gap | High | High | Critical | P0 |
| Currency Conversion | Medium | High | Critical | P0 |
| API Inconsistencies | Medium | Medium | High | P1 |
| Input Validation | Low | High | High | P1 |
| Replay Attacks | Medium | Medium | Medium | P2 |
| Error Disclosure | Low | Medium | Medium | P2 |

---

## 🔐 **Security Recommendations Summary**

1. **Implement comprehensive IP whitelisting**
2. **Fix currency conversion logic immediately**
3. **Standardize API endpoints and authentication**
4. **Add webhook replay attack prevention**
5. **Enhance input validation and sanitization**
6. **Implement security monitoring and alerting**
7. **Conduct regular security assessments**
8. **Document security procedures and incident response**

**Next Review Date**: 2025-02-21  
**Recommended Review Frequency**: Monthly
