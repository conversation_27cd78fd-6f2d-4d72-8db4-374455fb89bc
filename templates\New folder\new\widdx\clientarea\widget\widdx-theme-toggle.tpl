<!-- Theme Toggle Button for Navbar -->
<li class="nav-item">
  <button class="nav-link nav-btn ww-theme-toggle"
          type="button"
          aria-label="{lang key='theme.toggle'}"
          title="{lang key='theme.switchMode'}"
          id="theme-toggle-btn">
    <div class="nav-icon-circle">
      <div class="ww-theme-light" data-title="{lang key='theme.dark'}">
        <i class="fas fa-moon" aria-hidden="true"></i>
      </div>
      <div class="ww-theme-dark" data-title="{lang key='theme.light'}">
        <i class="fas fa-sun" aria-hidden="true"></i>
      </div>
    </div>
  </button>
</li>

<style>
/* Theme Toggle Specific Styles */
.ww-theme-toggle {
  position: relative;
  overflow: hidden;
}

.ww-theme-light,
.ww-theme-dark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

/* Light mode - show moon icon */
.ww-theme-light {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.ww-theme-dark {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

/* Dark mode - show sun icon */
[data-bs-theme="dark"] .ww-theme-light {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

[data-bs-theme="dark"] .ww-theme-dark {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Hover effects */
.ww-theme-toggle:hover .ww-theme-light,
.ww-theme-toggle:hover .ww-theme-dark {
  transform: translate(-50%, -50%) scale(1.1);
}

/* Focus styles for accessibility */
.ww-theme-toggle:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}
</style>
