<li class="nav-item dropdown">
    <a class="nav-link  dropdown-toggle" href="#" id="notifDropdown" role="button" data-bs-toggle="dropdown"
       aria-haspopup="true" aria-expanded="false">
        <i class="fas fa-bell"></i>
        {if count($clientAlerts) > 0}
            <span class="badge bg-danger rounded-pill">{count($clientAlerts)}</span>
        {else}
            <span class="notification">0</span>
        {/if}
    </a>
    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notifDropdown">
        <li>
            <div class="dropdown-header">
                {if count($clientAlerts) > 0}
                    You have {count($clientAlerts)} new notifications
                {else}
                    <span class="notification">You have 0 new notifications</span>
                {/if}
            </div>
        </li>
        <li>
            <div class="dropdown-divider"></div>
        </li>
        <li>
            <div class="notif-scroll" style="max-height: 400px; overflow-y: auto;">
                <div class="notif-center">
                    {foreach $clientAlerts as $alert}
                        <a class="dropdown-item" href="{$alert->getLink()}">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <i class="fas fa-{if $alert->getSeverity() == 'danger'}exclamation-circle{elseif $alert->getSeverity() == 'warning'}exclamation-triangle{elseif $alert->getSeverity() == 'info'}info-circle{else}check-circle{/if} fa-fw"></i>
                                </div>
                                <div>
                                    <span>{$alert->getMessage()}</span>
                                </div>
                            </div>
                        </a>
                    {foreachelse}
                        <div class="dropdown-item text-center">
                            {lang key='notificationsnone'}
                        </div>
                    {/foreach}
                </div>
            </div>
        </li>
    </ul>
</li>
