{
    "timestamp": "2025-06-28 01:48:22",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751068102",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/EAK5LfLMCt",
            "access_code": "EAK5LfLMCt",
            "reference": "INV-1-1751068102"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 01:48:25",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751068104",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/RO2J3cH5Vi",
            "access_code": "RO2J3cH5Vi",
            "reference": "INV-1-1751068104"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:11:59",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069518",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/4N56FvNOT0",
            "access_code": "4N56FvNOT0",
            "reference": "INV-1-1751069518"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:12:00",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069520",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/mtGyvDsIdF",
            "access_code": "mtGyvDsIdF",
            "reference": "INV-1-1751069520"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:12:51",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069570",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/GQyVbiASFU",
            "access_code": "GQyVbiASFU",
            "reference": "INV-1-1751069570"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:22:28",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751070148",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/y4kTaoWER8",
            "access_code": "y4kTaoWER8",
            "reference": "INV-1-1751070148"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:22:30",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751070149",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/Kz0ha4Qt46",
            "access_code": "Kz0ha4Qt46",
            "reference": "INV-1-1751070149"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-07-11 13:00:34",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1752231633",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/AwFH9KE99P",
            "access_code": "AwFH9KE99P",
            "reference": "INV-1-1752231633"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-07-18 13:00:39",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 110,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1752836439",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/4HyWp37wer",
            "access_code": "4HyWp37wer",
            "reference": "INV-1-1752836439"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-07-22 01:12:50",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-7855-1753139570",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":7855,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/qY049peVkQ",
            "access_code": "qY049peVkQ",
            "reference": "INV-7855-1753139570"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 7855
    }
}
{
    "timestamp": "2025-07-22 01:12:51",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-1707-1753139570",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":1707,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/Rhz10BuGpO",
            "access_code": "Rhz10BuGpO",
            "reference": "INV-1707-1753139570"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1707
    }
}
{
    "timestamp": "2025-07-22 01:12:51",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-9997-1753139571",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":9997,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/stEwMw5Y0J",
            "access_code": "stEwMw5Y0J",
            "reference": "INV-9997-1753139571"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 9997
    }
}
{
    "timestamp": "2025-07-22 01:12:52",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-5374-1753139571",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":5374,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/ING2pjH43K",
            "access_code": "ING2pjH43K",
            "reference": "INV-5374-1753139571"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 5374
    }
}
{
    "timestamp": "2025-07-22 01:12:52",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-3461-1753139572",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":3461,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/0qNCtmjm7o",
            "access_code": "0qNCtmjm7o",
            "reference": "INV-3461-1753139572"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 3461
    }
}
{
    "timestamp": "2025-07-22 01:12:53",
    "action": "CreatePayment",
    "http_code": 400,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 0,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-3455-1753139572",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":3455,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": false,
        "message": "Amount cannot be equal or less than zero"
    },
    "error": "",
    "params": {
        "invoiceid": 3455
    }
}
{
    "timestamp": "2025-07-22 01:12:53",
    "action": "CreatePayment",
    "http_code": 400,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": -5000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-5768-1753139573",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":5768,\"customer_name\":\"Test User\",\"whmcs_version\":null}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": false,
        "message": "Amount cannot be equal or less than zero"
    },
    "error": "",
    "params": {
        "invoiceid": 5768
    }
}
{
    "timestamp": "2025-07-22 01:13:43",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-4312-1753139622",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":4312,\"customer_name\":\"Test User\",\"whmcs_version\":\"unknown\"}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/XJz68Bs0Vz",
            "access_code": "XJz68Bs0Vz",
            "reference": "INV-4312-1753139622"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 4312
    }
}
{
    "timestamp": "2025-07-22 01:13:43",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-7532-1753139623",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":7532,\"customer_name\":\"Test User\",\"whmcs_version\":\"unknown\"}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/bWdeXI9QXD",
            "access_code": "bWdeXI9QXD",
            "reference": "INV-7532-1753139623"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 7532
    }
}
{
    "timestamp": "2025-07-22 01:13:43",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-5606-1753139623",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":5606,\"customer_name\":\"Test User\",\"whmcs_version\":\"unknown\"}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/c0tmPmqNu1",
            "access_code": "c0tmPmqNu1",
            "reference": "INV-5606-1753139623"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 5606
    }
}
{
    "timestamp": "2025-07-22 01:13:44",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-9972-1753139623",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":9972,\"customer_name\":\"Test User\",\"whmcs_version\":\"unknown\"}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/bWSXn2vvnD",
            "access_code": "bWSXn2vvnD",
            "reference": "INV-9972-1753139623"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 9972
    }
}
{
    "timestamp": "2025-07-22 01:13:44",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 10000,
            "email": "<EMAIL>",
            "currency": "ILS",
            "reference": "INV-6694-1753139624",
            "callback_url": "http:\/\/localhost\/whmcs\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/whmcs\/viewinvoice.php",
            "metadata": "{\"invoice_id\":6694,\"customer_name\":\"Test User\",\"whmcs_version\":\"unknown\"}",
            "first_name": "Test",
            "last_name": "User"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/UmiY7yjyhh",
            "access_code": "UmiY7yjyhh",
            "reference": "INV-6694-1753139624"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 6694
    }
}
