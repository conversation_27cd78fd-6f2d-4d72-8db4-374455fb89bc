const CACHE_NAME = 'widdx-backend-v1';
const ASSETS_TO_CACHE = [
  '/templates/widdx/clientarea/',
  '/templates/widdx/clientarea/assets/vendor/css/core.css',
  '/templates/widdx/clientarea/assets/vendor/css/theme-default.css',
  '/templates/widdx/clientarea/assets/css/widdx.css',
  '/templates/widdx/clientarea/assets/vendor/fonts/boxicons.css',
  '/templates/widdx/clientarea/assets/vendor/js/menu.js',
  '/templates/widdx/clientarea/assets/js/main.js',
  '/templates/widdx/clientarea/assets/vendor/libs/jquery/jquery.js',
  '/templates/widdx/clientarea/assets/vendor/libs/popper/popper.js',
  '/templates/widdx/clientarea/assets/vendor/js/bootstrap.js',
  '/templates/widdx/clientarea/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js',
  '/templates/widdx/clientarea/assets/img/icons/icon-72x72.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-96x96.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-128x128.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-144x144.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-152x152.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-192x192.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-384x384.png',
  '/templates/widdx/clientarea/assets/img/icons/icon-512x512.png'
];

// Install Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(ASSETS_TO_CACHE);
      })
      .catch((error) => {
        console.error('Failed to cache assets:', error);
      })
  );
});

// Activate Service Worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Fetch Event Strategy (Cache First)
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version if found
        if (response) {
          return response;
        }

        // Otherwise, fetch from network
        return fetch(event.request)
          .then((response) => {
            // Check if we received a valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Add to cache
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });

            return response;
          });
      })
  );
});
