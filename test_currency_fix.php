<?php
/**
 * Test Currency Conversion Fix
 * 
 * Verify that the JOD currency conversion bug has been fixed
 */

// Mock WHMCS environment
define('WHMCS', true);

// Include the gateway file
require_once __DIR__ . '/modules/gateways/lahza.php';

echo "🧪 Testing Currency Conversion Fix\n";
echo str_repeat("=", 50) . "\n";

// Test cases
$testCases = [
    ['currency' => 'USD', 'amount' => 100.00, 'expected' => 10000],
    ['currency' => 'USD', 'amount' => 25.50, 'expected' => 2550],
    ['currency' => 'ILS', 'amount' => 100.00, 'expected' => 10000],
    ['currency' => 'ILS', 'amount' => 50.75, 'expected' => 5075],
    ['currency' => 'JOD', 'amount' => 100.000, 'expected' => 100000],
    ['currency' => 'JOD', 'amount' => 25.500, 'expected' => 25500],
    ['currency' => 'JOD', 'amount' => 1.000, 'expected' => 1000],
];

$passed = 0;
$failed = 0;

foreach ($testCases as $test) {
    try {
        $result = lahza_convertToSmallestUnit($test['amount'], $test['currency']);
        
        if ($result === $test['expected']) {
            echo "✅ {$test['currency']} {$test['amount']} → {$result} (correct)\n";
            $passed++;
        } else {
            echo "❌ {$test['currency']} {$test['amount']} → {$result} (expected {$test['expected']})\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "💥 {$test['currency']} {$test['amount']} → ERROR: {$e->getMessage()}\n";
        $failed++;
    }
}

// Test unsupported currency
try {
    lahza_convertToSmallestUnit(100.00, 'EUR');
    echo "❌ EUR should throw exception but didn't\n";
    $failed++;
} catch (Exception $e) {
    echo "✅ EUR correctly throws exception: {$e->getMessage()}\n";
    $passed++;
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 Test Results:\n";
echo "✅ Passed: {$passed}\n";
echo "❌ Failed: {$failed}\n";
echo "Success Rate: " . round(($passed / ($passed + $failed)) * 100, 1) . "%\n";

if ($failed === 0) {
    echo "\n🎉 All currency conversion tests passed!\n";
    echo "✅ JOD currency bug has been fixed successfully.\n";
} else {
    echo "\n⚠️ Some tests failed. Please review the implementation.\n";
}
