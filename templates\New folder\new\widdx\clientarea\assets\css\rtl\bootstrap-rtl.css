/*
 * Bootstrap RTL Support for WHMCS WIDDX Backend Theme
 */

/* Grid System */
.row {
  margin-right: -15px;
  margin-left: -15px;
}

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12,
.col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

/* Text Alignment */
.text-start {
  text-align: right !important;
}

.text-end {
  text-align: left !important;
}

/* Float */
.float-start {
  float: right !important;
}

.float-end {
  float: left !important;
}

/* Margin and Padding */
.ms-0 {
  margin-right: 0 !important;
  margin-left: auto !important;
}

.ms-1 {
  margin-right: 0.25rem !important;
  margin-left: auto !important;
}

.ms-2 {
  margin-right: 0.5rem !important;
  margin-left: auto !important;
}

.ms-3 {
  margin-right: 1rem !important;
  margin-left: auto !important;
}

.ms-4 {
  margin-right: 1.5rem !important;
  margin-left: auto !important;
}

.ms-5 {
  margin-right: 3rem !important;
  margin-left: auto !important;
}

.me-0 {
  margin-left: 0 !important;
  margin-right: auto !important;
}

.me-1 {
  margin-left: 0.25rem !important;
  margin-right: auto !important;
}

.me-2 {
  margin-left: 0.5rem !important;
  margin-right: auto !important;
}

.me-3 {
  margin-left: 1rem !important;
  margin-right: auto !important;
}

.me-4 {
  margin-left: 1.5rem !important;
  margin-right: auto !important;
}

.me-5 {
  margin-left: 3rem !important;
  margin-right: auto !important;
}

.ps-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.ps-1 {
  padding-right: 0.25rem !important;
  padding-left: 0 !important;
}

.ps-2 {
  padding-right: 0.5rem !important;
  padding-left: 0 !important;
}

.ps-3 {
  padding-right: 1rem !important;
  padding-left: 0 !important;
}

.ps-4 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important;
}

.ps-5 {
  padding-right: 3rem !important;
  padding-left: 0 !important;
}

.pe-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.pe-1 {
  padding-left: 0.25rem !important;
  padding-right: 0 !important;
}

.pe-2 {
  padding-left: 0.5rem !important;
  padding-right: 0 !important;
}

.pe-3 {
  padding-left: 1rem !important;
  padding-right: 0 !important;
}

.pe-4 {
  padding-left: 1.5rem !important;
  padding-right: 0 !important;
}

.pe-5 {
  padding-left: 3rem !important;
  padding-right: 0 !important;
}

/* Border Radius */
.rounded-start {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-end {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

/* List Groups */
.list-group {
  padding-right: 0;
}

/* Form Controls */
.form-check {
  padding-right: 1.5rem;
  padding-left: 0;
}

.form-check .form-check-input {
  float: right;
  margin-right: -1.5rem;
  margin-left: 0;
}

.form-check-inline {
  margin-right: 0;
  margin-left: 0.75rem;
}

.form-check-inline .form-check-input {
  margin-right: 0;
  margin-left: 0.3125rem;
}

.form-select {
  padding: 0.375rem 0.75rem 0.375rem 2.25rem;
  background-position: left 0.75rem center;
}

/* Input Groups */
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-right: -1px;
  margin-left: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

/* Dropdown */
.dropdown-menu {
  text-align: right;
}

.dropdown-menu-end {
  right: auto;
  left: 0;
}

.dropdown-menu-start {
  right: 0;
  left: auto;
}

/* Pagination */
.pagination {
  padding-right: 0;
}

/* Alerts */
.alert-dismissible {
  padding-right: 1rem;
  padding-left: 4rem;
}

.alert-dismissible .btn-close {
  left: 0;
  right: auto;
}

/* Modal */
.modal-header .btn-close {
  margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Carousel */
.carousel-control-prev {
  right: 0;
  left: auto;
}

.carousel-control-next {
  left: 0;
  right: auto;
}

.carousel-indicators {
  right: 0;
  left: 0;
}

/* Offcanvas */
.offcanvas-start {
  right: 0;
  left: auto;
  transform: translateX(100%);
}

.offcanvas-end {
  left: 0;
  right: auto;
  transform: translateX(-100%);
}

/* Tooltip and Popover */
.tooltip, .popover {
  text-align: right;
}

/* Accordion */
.accordion-button::after {
  margin-left: 0;
  margin-right: auto;
}

/* Progress */
.progress-bar {
  float: right;
}

/* Spinners */
.spinner-border, .spinner-grow {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Toast */
.toast-header .btn-close {
  margin-left: -0.375rem;
  margin-right: 0.75rem;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
  padding-left: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  float: right;
  padding-left: 0.5rem;
  padding-right: 0;
}

/* Navbar */
.navbar-brand {
  margin-right: 0;
  margin-left: 1rem;
}

.navbar-nav {
  padding-right: 0;
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0.5rem;
}

.navbar-nav .dropdown-menu {
  right: 0;
  left: auto;
}

/* Card */
.card-header-tabs {
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.card-header-pills {
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

/* Close Button */
.btn-close {
  margin-right: auto;
  margin-left: 0;
}
