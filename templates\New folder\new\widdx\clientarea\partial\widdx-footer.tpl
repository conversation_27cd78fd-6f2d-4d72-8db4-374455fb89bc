</div>
</div>

{if !$inShoppingCart && $secondarySidebar->hasChildren()}
  <div class="d-lg-none sidebar sidebar-secondary">
    {include file="$template/clientarea/inc/widdx-sidebar.tpl" sidebar=$secondarySidebar}
  </div>
{/if}

<div class="clearfix"></div>
</div>
<!-- / Content -->

<!-- Footer -->
<widdxfooter class="content-widdxfooter widdxfooter bg-widdxfooter-theme">
  <div class="container-xxl d-flex flex-wrap justify-content-between py-2 flex-md-row flex-column">
    <div class="mb-2 mb-md-0">
      {lang key="copyrightFooterNotice" year=$date_year company=$companyname}
    </div>
    <div>
    {include file="$template/includes/social-accounts.tpl"}
    </div>
  </div>
</widdxfooter>
<!-- / Footer -->

<div class="content-backdrop fade"></div>
</div>
<!-- / Content wrapper -->

</div>
<!-- / Layout page -->

</div>
<!-- / Layout container -->

<!-- Overlay -->
<div class="layout-overlay layout-menu-toggle"></div>
</div>
<!-- / Layout wrapper -->

<!-- Modal for AJAX content -->
<div class="modal system-modal fade" id="modalAjax" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title"></h5>
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">{lang key='close'}</span>
        </button>
      </div>
      <div class="modal-body">
        {lang key='loading'}
        <!-- Display loading text -->
      </div>
      <div class="modal-footer">
        <div class="float-left loader">
          <i class="fas fa-circle-notch fa-spin"></i>
          {lang key='loading'}
          <!-- Display loading text -->
        </div>
        <button type="button" class="btn btn-default" data-dismiss="modal">
          {lang key='close'}
        </button>
        <button type="button" class="btn btn-primary modal-submit">
          {lang key='submit'}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Language and Currency selection modal -->
<form method="get" action="{$currentpagelinkback}">
  <div class="modal modal-localisation" id="modalChooseLanguage" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <button type="button" class="close text-light" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>

          {if $languagechangeenabled && count($locales) > 1}
            <h5 class="h5 pt-5 pb-3">{lang key='chooselanguage'}</h5>
            <div class="row item-selector">
              <input type="hidden" name="language" data-current="{$language}" value="{$language}" />
              {foreach $locales as $locale}
                <div class="col-4">
                  <a href="#" class="item{if $language == $locale.language} active{/if}" data-value="{$locale.language}">
                    {$locale.localisedName}
                  </a>
                </div>
              {/foreach}
            </div>
          {/if}

          {if !$loggedin && $currencies}
            <p class="h5 pt-5 pb-3">{lang key='choosecurrency'}</p>
            <div class="row item-selector">
              <input type="hidden" name="currency" data-current="{$activeCurrency.id}" value="">
              {foreach $currencies as $selectCurrency}
                <div class="col-4">
                  <a href="#" class="item{if $activeCurrency.id == $selectCurrency.id} active{/if}"
                    data-value="{$selectCurrency.id}">
                    {$selectCurrency.prefix} {$selectCurrency.code}
                  </a>
                </div>
              {/foreach}
            </div>
          {/if}
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-default">{lang key='apply'}</button>
        </div>
      </div>
    </div>
  </div>
</form>
{if !$loggedin && $adminLoggedIn}
  <a href="{$WEB_ROOT}/logout.php?returntoadmin=1" class="btn btn-return-to-admin" data-toggle="tooltip"
    data-placement="bottom"
    title="{if $adminMasqueradingAsClient}{lang key='adminmasqueradingasclient'} {lang key='logoutandreturntoadminarea'}{else}{lang key='adminloggedin'} {lang key='returntoadminarea'}{/if}">
    <i class="fas fa-redo-alt"></i>
    <span>{lang key="admin.returnToAdmin"}</span>
  </a>
{/if}

{include file="$template/includes/generate-password.tpl"}
{$footeroutput}


{include file="$template/clientarea/partial/widdx-mobile-menu.tpl"}


<!-- Core JS -->
<!-- build:js assets/vendor/js/core.js -->
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/jquery/jquery.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.0.0/crypto-js.min.js"></script>
<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', function() {
    var clientEmail = '{$client.email}'; // Get the email from Smarty variable

    if (clientEmail && validateEmail(clientEmail)) {
      clientEmail = clientEmail.trim().toLowerCase();
      var gravatarHash = CryptoJS.MD5(clientEmail).toString();
      var gravatarUrl = "https://www.gravatar.com/avatar/" + gravatarHash +
        "?s=200&d=mm"; // Image size 200x200

      var profileImg = document.getElementById('profile-img');
      var profilePlaceholder = document.getElementById('profile-placeholder');
      if (profileImg) {
        profileImg.src = gravatarUrl;
        profileImg.onload = function() {
          profileImg.style.display = 'block';
          profilePlaceholder.style.display = 'none';
        };
        profileImg.onerror = function() {
          profileImg.style.display = 'none';
          profilePlaceholder.style.display = 'block';
        };
      }
    } else {
      console.error("Invalid email address:", clientEmail);
    }
  });

  function validateEmail(email) {
    var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }
</script>

<script>
  // Enable noConflict mode for the new version of jQuery
  jQuery.noConflict(true);
</script>

<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/popper/popper.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/js/bootstrap.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js">
</script>
<!-- endbuild -->

<!-- Vendors JS -->
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/apex-charts/apexcharts.js"></script>

<!-- Main JS -->
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/main.js"></script>

<!-- Performance Optimizer (Load early for better performance) -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/performance-optimizer.js"></script>

<!-- Page JS -->
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/dashboards-analytics.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/widdx.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/progressbar.min.js"></script>

</body>

</html>