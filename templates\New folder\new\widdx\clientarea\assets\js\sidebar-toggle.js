document.addEventListener('DOMContentLoaded', function() {
    var menuToggle = document.querySelector('.layout-menu-toggle');
    var layout = document.querySelector('.layout-wrapper');
    var sidebar = document.querySelector('#layout-menu');

    if (menuToggle && layout && sidebar) {
        // وظيفة تبديل القائمة الجانبية
        function toggleSidebar() {
            layout.classList.toggle('layout-menu-expanded');
            
            // تحديث أيقونة القائمة
            var icon = menuToggle.querySelector('i');
            if (icon) {
                icon.classList.toggle('bx-menu');
                icon.classList.toggle('bx-x');
            }

            // حفظ حالة القائمة
            localStorage.setItem('menuExpanded', layout.classList.contains('layout-menu-expanded'));
        }

        // إضافة مستمع الحدث لزر التبديل
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSidebar();
        });

        // استعادة حالة القائمة عند تحميل الصفحة
        if (localStorage.getItem('menuExpanded') === 'true') {
            layout.classList.add('layout-menu-expanded');
        }

        // إغلاق القائمة عند النقر خارجها على الشاشات الصغيرة
        document.addEventListener('click', function(e) {
            if (window.innerWidth < 1200 && !sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                layout.classList.remove('layout-menu-expanded');
                var icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.classList.add('bx-menu');
                    icon.classList.remove('bx-x');
                }
            }
        });
    }
});