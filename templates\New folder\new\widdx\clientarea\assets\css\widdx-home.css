/*!
 * WIDDX Client Area Home Styles
 * Enhanced dashboard with dark/light mode support and RTL compatibility
 * Copyright (c) 2024 WIDDX Template
 */

/* ===== CSS VARIABLES ===== */
:root {
  --home-card-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
  --home-card-shadow-hover: 0 4px 12px 0 rgba(67, 89, 113, 0.16);
  --home-card-border-radius: 0.5rem;
  --home-stat-icon-size: 2.5rem;
  --home-progress-height: 0.5rem;
  --home-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-bs-theme="dark"] {
  --home-card-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
  --home-card-shadow-hover: 0 4px 12px 0 rgba(0, 0, 0, 0.4);
}

/* ===== DASHBOARD STATS CONTAINER ===== */
.dashboard-stats-container {
  margin-bottom: 2rem;
}

.dashboard-stats-container .row {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 1rem;
}

/* ===== DASHBOARD STAT CARDS ===== */
.dashboard-stat-card {
  height: 100%;
  transition: var(--home-transition);
}

.dashboard-stat-card .card {
  border: 1px solid rgba(var(--bs-border-color-rgb), 0.125);
  box-shadow: var(--home-card-shadow);
  border-radius: var(--home-card-border-radius);
  transition: var(--home-transition);
  overflow: hidden;
  position: relative;
}

.dashboard-stat-card .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-primary-dark, var(--bs-primary)));
  opacity: 0;
  transition: var(--home-transition);
}

.dashboard-stat-card:hover .card {
  box-shadow: var(--home-card-shadow-hover);
  transform: translateY(-2px);
}

.dashboard-stat-card:hover .card::before {
  opacity: 1;
}

.dashboard-stat-card .card-link {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

.dashboard-stat-card .card-link:hover {
  text-decoration: none;
  color: inherit;
}

/* ===== STAT ICON CIRCLES ===== */
.stat-icon-circle {
  width: var(--home-stat-icon-size);
  height: var(--home-stat-icon-size);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: var(--home-transition);
  position: relative;
  overflow: hidden;
}

.stat-icon-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  border-radius: inherit;
  transition: var(--home-transition);
}

.dashboard-stat-card:hover .stat-icon-circle::before {
  opacity: 0.2;
  transform: scale(1.1);
}

/* Color variants */
.bg-primary-soft {
  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  color: var(--bs-primary) !important;
}

.bg-success-soft {
  background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  color: var(--bs-success) !important;
}

.bg-warning-soft {
  background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
  color: var(--bs-warning) !important;
}

.bg-info-soft {
  background-color: rgba(var(--bs-info-rgb), 0.1) !important;
  color: var(--bs-info) !important;
}

/* ===== STAT NUMBERS ===== */
.stat-number {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.25rem;
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, var(--bs-primary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: var(--home-transition);
}

.dashboard-stat-card:hover .stat-number {
  transform: scale(1.05);
}

/* ===== PROGRESS BARS ===== */
.progress {
  height: var(--home-progress-height);
  border-radius: calc(var(--home-progress-height) / 2);
  background-color: rgba(var(--bs-secondary-rgb), 0.1);
  overflow: hidden;
}

.progress-bar {
  border-radius: inherit;
  transition: width 0.6s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== CLIENT HOME PANELS ===== */
.client-home-panels {
  margin-top: 2rem;
}

.client-home-panels .card {
  border: 1px solid rgba(var(--bs-border-color-rgb), 0.125);
  box-shadow: var(--home-card-shadow);
  border-radius: var(--home-card-border-radius);
  transition: var(--home-transition);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.client-home-panels .card:hover {
  box-shadow: var(--home-card-shadow-hover);
  transform: translateY(-1px);
}

/* Card accent colors */
.card-accent-primary {
  border-top: 3px solid var(--bs-primary);
}

.card-accent-success {
  border-top: 3px solid var(--bs-success);
}

.card-accent-warning {
  border-top: 3px solid var(--bs-warning);
}

.card-accent-info {
  border-top: 3px solid var(--bs-info);
}

.card-accent-danger {
  border-top: 3px solid var(--bs-danger);
}

/* ===== CARD HEADERS ===== */
.client-home-panels .card-header {
  background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.02), rgba(var(--bs-primary-rgb), 0.05));
  border-bottom: 1px solid rgba(var(--bs-border-color-rgb), 0.125);
  padding: 1px 10px 0px 10px !important
}

.client-home-panels .card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--bs-heading-color);
  margin: 0;
}

.client-home-panels .card-title i {
  margin-right: 0.5rem;
  color: var(--bs-primary);
  font-size: 1rem;
}

/* ===== LIST GROUPS ===== */
.client-home-panels .list-group-item {
  border: none;
  border-bottom: 1px solid rgba(var(--bs-border-color-rgb), 0.125);
  padding: 1rem 1.5rem;
  transition: var(--home-transition);
  position: relative;
}

.client-home-panels .list-group-item:last-child {
  border-bottom: none;
}

.client-home-panels .list-group-item:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.02);
  padding-left: 1.75rem;
}

.client-home-panels .list-group-item.active {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  border-left: 3px solid var(--bs-primary);
}

.client-home-panels .list-group-item i {
  color: var(--bs-secondary-color);
  transition: var(--home-transition);
  width: 1.25rem;
  text-align: center;
}

.client-home-panels .list-group-item:hover i,
.client-home-panels .list-group-item.active i {
  color: var(--bs-primary);
  transform: scale(1.1);
}

/* ===== RTL SUPPORT ===== */
[dir="rtl"] .client-home-panels .card-title i {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .client-home-panels .list-group-item:hover {
  padding-left: 1.5rem;
  padding-right: 1.75rem;
}

[dir="rtl"] .client-home-panels .list-group-item.active {
  border-left: none;
  border-right: 3px solid var(--bs-primary);
}

/* ===== DARK MODE ADJUSTMENTS ===== */
[data-bs-theme="dark"] .dashboard-stat-card .card {
  background-color: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .client-home-panels .card {
  background-color: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .client-home-panels .card-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.05));
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .client-home-panels .list-group-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .client-home-panels .list-group-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .progress {
  background-color: rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 575.98px) {
  .dashboard-stats-container .row {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
  }
  
  .dashboard-stat-card .card-body {
    padding: 1rem !important;
  }
  
  .stat-number {
    font-size: 1.75rem;
  }
  
  .client-home-panels .card-header {
    padding: 0 !important;
  }

  .client-home-panels .list-group-item {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 767.98px) {
  .client-home-panels .card-title {
    font-size: 1rem;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .dashboard-stat-card,
  .dashboard-stat-card .card,
  .stat-icon-circle,
  .stat-number,
  .progress-bar,
  .client-home-panels .card,
  .client-home-panels .list-group-item {
    transition: none;
  }
  
  .progress-bar::before {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .dashboard-stat-card .card,
  .client-home-panels .card {
    border: 2px solid var(--bs-border-color);
  }
  
  .client-home-panels .list-group-item.active {
    border-left-width: 4px;
  }
  
  [dir="rtl"] .client-home-panels .list-group-item.active {
    border-right-width: 4px;
  }
}
