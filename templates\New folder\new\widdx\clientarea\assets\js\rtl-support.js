/**
 * WHMCS WIDDX RTL Support
 * Handles RTL-specific functionality
 */

document.addEventListener('DOMContentLoaded', function() {
  'use strict';

  console.log('RTL Support Initialized');

  // Check if the page is in RTL mode
  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';

  if (isRTL) {
    console.log('RTL mode detected');

    // Add RTL class to body for global styling
    document.body.classList.add('rtl-mode');

    // Fix layout container for RTL
    var layoutContainer = document.querySelector('.layout-container');
    if (layoutContainer) {
      layoutContainer.style.flexDirection = 'row-reverse';
    }

    // Fix navbar container for RTL
    var navbarContainer = document.querySelector('.layout-navbar .container-xxl');
    if (navbarContainer) {
      navbarContainer.style.flexDirection = 'row-reverse';

      // Ensure search is on right and icons on left
      var searchWrapper = navbarContainer.querySelector('.navbar-search-wrapper');
      if (searchWrapper) {
        searchWrapper.style.marginLeft = '0';
        searchWrapper.style.marginRight = 'auto';
        searchWrapper.style.order = '1';
      }

      var navbarNavRight = navbarContainer.querySelector('.navbar-nav-right');
      if (navbarNavRight) {
        navbarNavRight.style.marginLeft = 'auto';
        navbarNavRight.style.marginRight = '0';
        navbarNavRight.style.order = '3';
      }

      var navbarBrand = navbarContainer.querySelector('.navbar-brand');
      if (navbarBrand) {
        navbarBrand.style.order = '2';
      }
    }

    // Apply immediate fixes to navbar elements
    var navbarElements = document.querySelectorAll('.layout-navbar .navbar-nav');
    navbarElements.forEach(function(element) {
      element.style.flexDirection = 'row-reverse';

      // Add proper spacing for icon distribution
      if (element.classList.contains('flex-row') || element.classList.contains('align-items-center')) {
        element.style.gap = '1rem';
        element.style.justifyContent = 'flex-end';

        // Fix individual nav items
        var navItems = element.querySelectorAll('.nav-item');
        navItems.forEach(function(item) {
          item.style.marginLeft = '0';
          item.style.marginRight = '0';
        });
      }
    });

    // Fix menu toggle icons for RTL
    var menuToggles = document.querySelectorAll('.menu-toggle');
    menuToggles.forEach(function(toggle) {
      toggle.classList.add('rtl-toggle');
    });

    // Fix dropdown positioning for RTL
    var dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(function(dropdown) {
      dropdown.classList.add('dropdown-menu-end');
    });

    // Fix input groups for RTL
    var inputGroups = document.querySelectorAll('.input-group');
    inputGroups.forEach(function(group) {
      group.classList.add('rtl-input-group');
    });

    // Fix any elements with directional icons
    var rightIcons = document.querySelectorAll('.bx-chevron-right, .fa-chevron-right');
    rightIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-right')) {
        icon.classList.remove('bx-chevron-right');
        icon.classList.add('bx-chevron-left');
      } else if (icon.classList.contains('fa-chevron-right')) {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-left');
      }
    });

    var leftIcons = document.querySelectorAll('.bx-chevron-left, .fa-chevron-left');
    leftIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-left')) {
        icon.classList.remove('bx-chevron-left');
        icon.classList.add('bx-chevron-right');
      } else if (icon.classList.contains('fa-chevron-left')) {
        icon.classList.remove('fa-chevron-left');
        icon.classList.add('fa-chevron-right');
      }
    });

    // Fix layout for RTL
    document.body.classList.add('rtl-layout');

    // Fix search box for RTL
    var searchBoxes = document.querySelectorAll('.navbar-search');
    searchBoxes.forEach(function(searchBox) {
      searchBox.classList.add('rtl-search');
    });

    // Fix navbar specific elements for RTL
    var navbar = document.querySelector('.layout-navbar');
    if (navbar) {
      // Fix navbar brand
      var navbarBrand = navbar.querySelector('.navbar-brand');
      if (navbarBrand) {
        navbarBrand.style.marginRight = '0';
        navbarBrand.style.marginLeft = '1rem';
      }

      // Fix navbar collapse
      var navbarCollapse = navbar.querySelector('.navbar-collapse');
      if (navbarCollapse) {
        navbarCollapse.style.flexDirection = 'row-reverse';
      }

      // Fix navbar nav right
      var navbarNavRight = navbar.querySelector('.navbar-nav-right');
      if (navbarNavRight) {
        navbarNavRight.style.marginLeft = '0';
        navbarNavRight.style.marginRight = 'auto';
      }

      // Fix navbar search forms
      var searchForms = navbar.querySelectorAll('form');
      searchForms.forEach(function(form) {
        form.style.flexDirection = 'row-reverse';
      });

      // Fix navbar icons
      var navIcons = navbar.querySelectorAll('.nav-icon');
      navIcons.forEach(function(icon) {
        icon.style.marginRight = '0';
        icon.style.marginLeft = '0.5rem';
      });

      // Fix badge positions
      var badges = navbar.querySelectorAll('.badge-number');
      badges.forEach(function(badge) {
        badge.style.right = 'auto';
        badge.style.left = '0';
      });
    }

    // Listen for dynamic content changes
    var observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.addedNodes.length) {
          // Check for newly added elements that need RTL fixes
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              // Fix menu toggles
              var newToggles = node.querySelectorAll ? node.querySelectorAll('.menu-toggle') : [];
              newToggles.forEach(function(toggle) {
                toggle.classList.add('rtl-toggle');
              });

              // Fix dropdowns
              var newDropdowns = node.querySelectorAll ? node.querySelectorAll('.dropdown-menu') : [];
              newDropdowns.forEach(function(dropdown) {
                dropdown.classList.add('dropdown-menu-end');
              });

              // Fix input groups
              var newInputGroups = node.querySelectorAll ? node.querySelectorAll('.input-group') : [];
              newInputGroups.forEach(function(group) {
                group.classList.add('rtl-input-group');
              });

              // Fix search boxes
              var newSearchBoxes = node.querySelectorAll ? node.querySelectorAll('.navbar-search') : [];
              newSearchBoxes.forEach(function(searchBox) {
                searchBox.classList.add('rtl-search');
              });

              // Fix directional icons (both Boxicons and Font Awesome)
              var newRightIcons = node.querySelectorAll ?
                node.querySelectorAll('.bx-chevron-right, .fa-chevron-right') : [];
              newRightIcons.forEach(function(icon) {
                if (icon.classList.contains('bx-chevron-right')) {
                  icon.classList.remove('bx-chevron-right');
                  icon.classList.add('bx-chevron-left');
                } else if (icon.classList.contains('fa-chevron-right')) {
                  icon.classList.remove('fa-chevron-right');
                  icon.classList.add('fa-chevron-left');
                }
              });

              var newLeftIcons = node.querySelectorAll ?
                node.querySelectorAll('.bx-chevron-left, .fa-chevron-left') : [];
              newLeftIcons.forEach(function(icon) {
                if (icon.classList.contains('bx-chevron-left')) {
                  icon.classList.remove('bx-chevron-left');
                  icon.classList.add('bx-chevron-right');
                } else if (icon.classList.contains('fa-chevron-left')) {
                  icon.classList.remove('fa-chevron-left');
                  icon.classList.add('fa-chevron-right');
                }
              });

              // Fix card headers
              var newCardHeaders = node.querySelectorAll ? node.querySelectorAll('.card-header') : [];
              newCardHeaders.forEach(function(header) {
                if (!header.style.flexDirection) {
                  header.style.display = 'flex';
                  header.style.flexDirection = 'row-reverse';
                  header.style.justifyContent = 'space-between';
                  header.style.direction = 'ltr';
                }
              });

              // Fix navbar elements in dynamic content
              var newNavbar = node.querySelector ? node.querySelector('.layout-navbar') : null;
              if (newNavbar) {
                // Fix navbar brand
                var newNavbarBrand = newNavbar.querySelector('.navbar-brand');
                if (newNavbarBrand) {
                  newNavbarBrand.style.marginRight = '0';
                  newNavbarBrand.style.marginLeft = '1rem';
                }

                // Fix navbar collapse
                var newNavbarCollapse = newNavbar.querySelector('.navbar-collapse');
                if (newNavbarCollapse) {
                  newNavbarCollapse.style.flexDirection = 'row-reverse';
                }

                // Fix navbar nav right
                var newNavbarNavRight = newNavbar.querySelector('.navbar-nav-right');
                if (newNavbarNavRight) {
                  newNavbarNavRight.style.marginLeft = '0';
                  newNavbarNavRight.style.marginRight = 'auto';
                }

                // Fix navbar search forms
                var newSearchForms = newNavbar.querySelectorAll('form');
                newSearchForms.forEach(function(form) {
                  form.style.flexDirection = 'row-reverse';
                });

                // Fix navbar icons
                var newNavIcons = newNavbar.querySelectorAll('.nav-icon');
                newNavIcons.forEach(function(icon) {
                  icon.style.marginRight = '0';
                  icon.style.marginLeft = '0.5rem';
                });

                // Fix badge positions
                var newBadges = newNavbar.querySelectorAll('.badge-number');
                newBadges.forEach(function(badge) {
                  badge.style.right = 'auto';
                  badge.style.left = '0';
                });
              }
            }
          });
        }
      });
    });

    // Observe the entire document for changes
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Fix any RTL-specific layout issues on window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth <= 991.98) {
        // Mobile layout fixes for RTL
        var layoutMenu = document.querySelector('.layout-menu');
        if (layoutMenu) {
          layoutMenu.style.transform = 'translateX(100%)';
        }

        // Fix mobile navbar brand in RTL
        var navbarBrand = document.querySelector('.navbar-brand.mx-auto');
        if (navbarBrand) {
          navbarBrand.style.transform = 'translateX(50%)';
        }

        // Fix mobile menu toggle in RTL
        var menuToggle = document.querySelector('.layout-menu-toggle');
        if (menuToggle) {
          menuToggle.style.marginRight = '0';
          menuToggle.style.marginLeft = 'auto';
        }
      }
    });

    // Fix navbar logo and brand immediately
    var navbarLogo = document.querySelector('.app-brand-logo');
    if (navbarLogo) {
      // Ensure logo is properly aligned in RTL
      navbarLogo.style.marginRight = '0';
      navbarLogo.style.marginLeft = '0.5rem';
    }

    var navbarText = document.querySelector('.app-brand-text');
    if (navbarText) {
      // Ensure brand text is properly aligned in RTL
      navbarText.style.textAlign = 'right';
    }

    // Fix navbar icons distribution
    var navbarIconsContainer = document.querySelector('.navbar-nav.flex-row.align-items-center');
    if (navbarIconsContainer) {
      navbarIconsContainer.style.display = 'flex';
      navbarIconsContainer.style.gap = '1.25rem';
      navbarIconsContainer.style.justifyContent = 'flex-start';

      // Position the navbar-nav-right to the left in RTL
      var navbarNavRight = document.querySelector('.navbar-nav-right');
      if (navbarNavRight) {
        navbarNavRight.style.marginLeft = 'auto';
        navbarNavRight.style.marginRight = '0';
        navbarNavRight.style.justifyContent = 'flex-start';
      }

      // Fix individual icon items
      var iconItems = navbarIconsContainer.querySelectorAll('.nav-item');
      iconItems.forEach(function(item, index) {
        item.style.margin = '0';

        // Fix icon links
        var iconLink = item.querySelector('.nav-link');
        if (iconLink) {
          iconLink.style.padding = '0.5rem';
        }

        // Fix notification badges
        var badge = item.querySelector('.badge-number');
        if (badge) {
          badge.style.top = '0';
          badge.style.right = 'auto';
          badge.style.left = '0';
          badge.style.transform = 'translate(-50%, -50%)';
        }
      });
    }
  }
});
