/*! WHMCS WIDDX Theme - RTL Support v1.0.0 | Optimized & Minified */
/* RTL Variables */
[dir="rtl"]{--text-align-start:right;--text-align-end:left;--float-start:right;--float-end:left;--border-start:border-right;--border-end:border-left;--margin-start:margin-right;--margin-end:margin-left;--padding-start:padding-right;--padding-end:padding-left}
/* RTL Base Styles */
html[dir="rtl"]{direction:rtl}
html[dir="rtl"] body{text-align:right;direction:rtl}
html[dir="rtl"] .text-left{text-align:right!important}
html[dir="rtl"] .text-right{text-align:left!important}
html[dir="rtl"] .float-left{float:right!important}
html[dir="rtl"] .float-right{float:left!important}
html[dir="rtl"] .ml-auto{margin-right:auto!important;margin-left:0!important}
html[dir="rtl"] .mr-auto{margin-left:auto!important;margin-right:0!important}
html[dir="rtl"] .pl-0{padding-right:0!important;padding-left:inherit!important}
html[dir="rtl"] .pr-0{padding-left:0!important;padding-right:inherit!important}
html[dir="rtl"] .ml-1{margin-right:0.25rem!important;margin-left:0!important}
html[dir="rtl"] .mr-1{margin-left:0.25rem!important;margin-right:0!important}
html[dir="rtl"] .ml-2{margin-right:0.5rem!important;margin-left:0!important}
html[dir="rtl"] .mr-2{margin-left:0.5rem!important;margin-right:0!important}
html[dir="rtl"] .ml-3{margin-right:1rem!important;margin-left:0!important}
html[dir="rtl"] .mr-3{margin-left:1rem!important;margin-right:0!important}
html[dir="rtl"] .pl-1{padding-right:0.25rem!important;padding-left:0!important}
html[dir="rtl"] .pr-1{padding-left:0.25rem!important;padding-right:0!important}
html[dir="rtl"] .pl-2{padding-right:0.5rem!important;padding-left:0!important}
html[dir="rtl"] .pr-2{padding-left:0.5rem!important;padding-right:0!important}
html[dir="rtl"] .pl-3{padding-right:1rem!important;padding-left:0!important}
html[dir="rtl"] .pr-3{padding-left:1rem!important;padding-right:0!important}
/* RTL Bootstrap Grid */
html[dir="rtl"] .offset-1{margin-right:8.33333333%;margin-left:0}
html[dir="rtl"] .offset-2{margin-right:16.66666667%;margin-left:0}
html[dir="rtl"] .offset-3{margin-right:25%;margin-left:0}
html[dir="rtl"] .offset-4{margin-right:33.33333333%;margin-left:0}
html[dir="rtl"] .offset-5{margin-right:41.66666667%;margin-left:0}
html[dir="rtl"] .offset-6{margin-right:50%;margin-left:0}
html[dir="rtl"] .offset-7{margin-right:58.33333333%;margin-left:0}
html[dir="rtl"] .offset-8{margin-right:66.66666667%;margin-left:0}
html[dir="rtl"] .offset-9{margin-right:75%;margin-left:0}
html[dir="rtl"] .offset-10{margin-right:83.33333333%;margin-left:0}
html[dir="rtl"] .offset-11{margin-right:91.66666667%;margin-left:0}
/* RTL Navigation */
html[dir="rtl"] .navbar-nav{flex-direction:row-reverse}
html[dir="rtl"] .navbar-nav .nav-link{padding-right:0.5rem;padding-left:0.5rem}
html[dir="rtl"] .navbar-toggler{margin-left:0;margin-right:auto}
html[dir="rtl"] .navbar-brand{margin-right:0;margin-left:1rem}
html[dir="rtl"] .dropdown-menu{right:0;left:auto;text-align:right}
html[dir="rtl"] .dropdown-menu-right{right:auto;left:0}
html[dir="rtl"] .dropdown-menu-left{right:0;left:auto}
html[dir="rtl"] .dropdown-toggle::after{margin-right:0.255em;margin-left:0}
html[dir="rtl"] .dropup .dropdown-toggle::after{margin-right:0.255em;margin-left:0}
html[dir="rtl"] .dropright .dropdown-toggle::after{border-left:0.3em solid transparent;border-right:0;border-top:0.3em solid transparent;border-bottom:0.3em solid transparent}
html[dir="rtl"] .dropleft .dropdown-toggle::before{border-right:0.3em solid transparent;border-left:0;border-top:0.3em solid transparent;border-bottom:0.3em solid transparent}
/* RTL Forms */
html[dir="rtl"] .form-check{padding-right:1.25rem;padding-left:0}
html[dir="rtl"] .form-check-input{float:right;margin-right:-1.25rem;margin-left:0}
html[dir="rtl"] .form-check-reverse{padding-left:1.25rem;padding-right:0;text-align:left}
html[dir="rtl"] .form-check-reverse .form-check-input{float:left;margin-left:-1.25rem;margin-right:0}
html[dir="rtl"] .input-group-prepend{border-radius:0 0.25rem 0.25rem 0}
html[dir="rtl"] .input-group-append{border-radius:0.25rem 0 0 0.25rem}
html[dir="rtl"] .input-group>.form-control:not(:last-child){border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0.25rem;border-bottom-right-radius:0.25rem}
html[dir="rtl"] .input-group>.form-control:not(:first-child){border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:0.25rem;border-bottom-left-radius:0.25rem}
/* RTL Buttons */
html[dir="rtl"] .btn-group>.btn:not(:last-child):not(.dropdown-toggle){border-top-left-radius:0;border-bottom-left-radius:0}
html[dir="rtl"] .btn-group>.btn:not(:first-child){border-top-right-radius:0;border-bottom-right-radius:0}
html[dir="rtl"] .btn-group>.btn-group:not(:last-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}
html[dir="rtl"] .btn-group>.btn-group:not(:first-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}
/* RTL Tables */
html[dir="rtl"] .table{text-align:right}
html[dir="rtl"] .table th{text-align:right}
html[dir="rtl"] .table td{text-align:right}
html[dir="rtl"] .table-responsive{direction:rtl}
/* RTL Cards */
html[dir="rtl"] .card-header{text-align:right}
html[dir="rtl"] .card-body{text-align:right}
html[dir="rtl"] .card-footer{text-align:right}
html[dir="rtl"] .card-title{text-align:right}
html[dir="rtl"] .card-text{text-align:right}
/* RTL Modals */
html[dir="rtl"] .modal-header{text-align:right}
html[dir="rtl"] .modal-body{text-align:right}
html[dir="rtl"] .modal-footer{text-align:right;justify-content:flex-start}
html[dir="rtl"] .modal-footer .btn+.btn{margin-right:0.5rem;margin-left:0}
html[dir="rtl"] .close{float:left}
/* RTL Alerts */
html[dir="rtl"] .alert{text-align:right}
html[dir="rtl"] .alert-dismissible .close{right:auto;left:0;padding:0.75rem 1.25rem}
/* RTL Breadcrumbs */
html[dir="rtl"] .breadcrumb{direction:rtl}
html[dir="rtl"] .breadcrumb-item+.breadcrumb-item::before{float:right;padding-left:0;padding-right:0.5rem;content:"/"}
html[dir="rtl"] .breadcrumb-item+.breadcrumb-item{padding-right:0.5rem;padding-left:0}
/* RTL Pagination */
html[dir="rtl"] .pagination{direction:rtl}
html[dir="rtl"] .page-link{margin-right:-1px;margin-left:0}
html[dir="rtl"] .page-item:first-child .page-link{margin-right:0;border-top-right-radius:0.25rem;border-bottom-right-radius:0.25rem;border-top-left-radius:0;border-bottom-left-radius:0}
html[dir="rtl"] .page-item:last-child .page-link{border-top-left-radius:0.25rem;border-bottom-left-radius:0.25rem;border-top-right-radius:0;border-bottom-right-radius:0}
/* RTL List Groups */
html[dir="rtl"] .list-group{text-align:right}
html[dir="rtl"] .list-group-item{text-align:right}
html[dir="rtl"] .list-group-horizontal>.list-group-item:first-child{border-bottom-right-radius:0.25rem;border-top-right-radius:0.25rem;border-bottom-left-radius:0;border-top-left-radius:0}
html[dir="rtl"] .list-group-horizontal>.list-group-item:last-child{border-bottom-left-radius:0.25rem;border-top-left-radius:0.25rem;border-bottom-right-radius:0;border-top-right-radius:0}
/* RTL Progress */
html[dir="rtl"] .progress{direction:rtl}
html[dir="rtl"] .progress-bar{direction:ltr}
/* RTL Tooltips */
html[dir="rtl"] .tooltip{direction:rtl}
html[dir="rtl"] .tooltip-inner{text-align:right}
/* RTL Popovers */
html[dir="rtl"] .popover{direction:rtl}
html[dir="rtl"] .popover-body{text-align:right}
html[dir="rtl"] .popover-header{text-align:right}
/* RTL Custom Components */
html[dir="rtl"] .widdxsidebar{left:auto;right:0;transform:translateX(100%)}
html[dir="rtl"] .widdxsidebar.show{transform:translateX(0)}
html[dir="rtl"] .search-close{right:auto;left:0}
html[dir="rtl"] .search-submit{right:auto;left:20px}
html[dir="rtl"] .header .cart-btn i{margin-right:0;margin-left:5px}
html[dir="rtl"] .promo-banner .icon{float:right;margin:0 0 0 20px}
html[dir="rtl"] .promo-banner .content{margin-right:185px;margin-left:0}
html[dir="rtl"] .promo-banner .icon-left{float:right}
html[dir="rtl"] .promo-banner-slim .content{margin-right:110px;margin-left:0}
html[dir="rtl"] .promo-banner-slim .btn{margin:0 0 0 20px}
html[dir="rtl"] .social-icons .btn-icon i{margin-right:0;margin-left:5px}
html[dir="rtl"] .popup{right:auto;left:20px}
html[dir="rtl"] .close-lightbox{right:auto;left:10px}
html[dir="rtl"] .dashboard-stat-card .card-header{flex-direction:row-reverse}
html[dir="rtl"] .dashboard-stat-card .d-flex{flex-direction:row-reverse}
html[dir="rtl"] .client-home-panels .card-header{flex-direction:row-reverse}
/* RTL Media Queries */
@media(max-width:767.98px){html[dir="rtl"] .promo-banner .content{margin:10px 0 0 0}}
@media(max-width:767.98px){html[dir="rtl"] .promo-banner .icon-left{float:none}}
@media(max-width:767.98px){html[dir="rtl"] .promo-banner form{text-align:center}}
@media(max-width:650px){html[dir="rtl"] .promo-banner .icon{width:100%;margin-bottom:15px}}
@media(min-width:768px){html[dir="rtl"] .popup{flex-direction:row-reverse}}
@media(max-width:767px){html[dir="rtl"] .widdxsidebar{right:0;left:auto;transform:translateX(100%)}}
/* RTL Animations */
@keyframes slideInRight{from{transform:translateX(100%)}to{transform:translateX(0)}}
@keyframes slideOutRight{from{transform:translateX(0)}to{transform:translateX(100%)}}
html[dir="rtl"] .slide-in{animation:slideInRight 0.3s ease}
html[dir="rtl"] .slide-out{animation:slideOutRight 0.3s ease}
/* RTL Text Direction */
html[dir="rtl"] .ltr{direction:ltr!important;text-align:left!important}
html[dir="rtl"] .rtl{direction:rtl!important;text-align:right!important}
/* RTL Icons */
html[dir="rtl"] .fa-chevron-left:before{content:"\f054"}
html[dir="rtl"] .fa-chevron-right:before{content:"\f053"}
html[dir="rtl"] .fa-angle-left:before{content:"\f105"}
html[dir="rtl"] .fa-angle-right:before{content:"\f104"}
html[dir="rtl"] .fa-arrow-left:before{content:"\f061"}
html[dir="rtl"] .fa-arrow-right:before{content:"\f060"}
/* RTL Flexbox */
html[dir="rtl"] .justify-content-start{justify-content:flex-end!important}
html[dir="rtl"] .justify-content-end{justify-content:flex-start!important}
html[dir="rtl"] .align-self-start{align-self:flex-end!important}
html[dir="rtl"] .align-self-end{align-self:flex-start!important}
html[dir="rtl"] .flex-row-reverse{flex-direction:row!important}
html[dir="rtl"] .flex-row{flex-direction:row-reverse!important}
