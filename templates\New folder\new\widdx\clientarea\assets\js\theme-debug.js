/**
 * Theme System Debug for Backend
 */
console.log('Backend Theme Debug Script Loaded');

// Check if theme toggle button exists
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM Content Loaded (Backend)');
  
  var toggleButton = document.querySelector('.ww-theme-toggle');
  if (toggleButton) {
    console.log('Theme Toggle Button Found (Backend):', toggleButton);
    
    // Add a test click handler
    toggleButton.addEventListener('click', function(e) {
      console.log('Theme Toggle Button Clicked (Backend)');
      console.log('Current Theme (Backend):', document.documentElement.getAttribute('data-bs-theme'));
    });
  } else {
    console.error('Theme Toggle Button Not Found (Backend)');
  }
  
  // Check localStorage
  console.log('Current Theme in localStorage (Backend):', localStorage.getItem('theme'));
  
  // Check data-bs-theme attributes
  console.log('HTML data-bs-theme (Backend):', document.documentElement.getAttribute('data-bs-theme'));
  console.log('Body data-bs-theme (Backend):', document.body.getAttribute('data-bs-theme'));
});
