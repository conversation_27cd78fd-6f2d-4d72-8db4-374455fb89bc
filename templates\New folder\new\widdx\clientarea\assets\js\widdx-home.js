/*!
 * WIDDX Client Area Home JavaScript
 * Enhanced dashboard functionality with accessibility and performance optimizations
 * Copyright (c) 2024 WIDDX Template
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initHomeDashboard();
    });

    function initHomeDashboard() {
        // Initialize all dashboard components
        initStatCards();
        initHomePanels();
        initProgressBars();
        initAccessibility();
        initRTLSupport();
        initPerformanceOptimizations();
    }

    // Enhanced Stat Cards
    function initStatCards() {
        const statCards = document.querySelectorAll('.dashboard-stat-card');
        
        statCards.forEach(card => {
            // Add loading state management
            const cardLink = card.querySelector('.card-link');
            if (cardLink) {
                cardLink.addEventListener('click', function(e) {
                    // Add loading indicator
                    const loadingIndicator = createLoadingIndicator();
                    card.appendChild(loadingIndicator);
                    
                    // Remove loading indicator after navigation
                    setTimeout(() => {
                        if (loadingIndicator.parentNode) {
                            loadingIndicator.remove();
                        }
                    }, 1000);
                });
            }

            // Add hover analytics
            card.addEventListener('mouseenter', function() {
                logInteraction('stat_card_hover', card.dataset.cardType || 'unknown');
            });

            // Add keyboard navigation
            if (cardLink) {
                cardLink.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
            }
        });
    }

    // Enhanced Home Panels
    function initHomePanels() {
        const homePanels = document.querySelectorAll('.client-home-panels .card');
        
        homePanels.forEach(panel => {
            // Add panel collapse/expand functionality
            const header = panel.querySelector('.card-header');
            if (header && panel.querySelector('.card-body, .list-group')) {
                header.style.cursor = 'pointer';
                header.setAttribute('role', 'button');
                header.setAttribute('tabindex', '0');
                header.setAttribute('aria-expanded', 'true');
                
                // Add collapse icon
                const collapseIcon = document.createElement('i');
                collapseIcon.className = 'fas fa-chevron-up ms-auto panel-collapse-icon';
                collapseIcon.style.transition = 'transform 0.3s ease';
                header.appendChild(collapseIcon);

                header.addEventListener('click', function() {
                    togglePanel(panel);
                });

                header.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        togglePanel(panel);
                    }
                });
            }

            // Enhanced list item interactions
            const listItems = panel.querySelectorAll('.list-group-item-action');
            listItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Add ripple effect
                    createRippleEffect(e, this);
                    
                    // Log interaction
                    logInteraction('panel_item_click', this.getAttribute('menuItemName') || 'unknown');
                });
            });
        });
    }

    // Enhanced Progress Bars with Animation
    function initProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar');
        
        // Intersection Observer for animation on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateProgressBar(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        progressBars.forEach(bar => {
            observer.observe(bar);
            
            // Store original width
            const originalWidth = bar.style.width;
            bar.style.width = '0%';
            bar.dataset.targetWidth = originalWidth;
        });
    }

    // Accessibility Enhancements
    function initAccessibility() {
        // Add skip links
        addSkipLinks();
        
        // Enhance focus management
        enhanceFocusManagement();
        
        // Add ARIA live regions
        addLiveRegions();
        
        // Keyboard navigation improvements
        enhanceKeyboardNavigation();
    }

    // RTL Support Enhancements
    function initRTLSupport() {
        const isRTL = document.documentElement.dir === 'rtl' || 
                     document.body.classList.contains('rtl') ||
                     getComputedStyle(document.body).direction === 'rtl';

        if (isRTL) {
            document.body.classList.add('widdx-rtl');
            
            // Fix any RTL-specific issues
            fixRTLIcons();
            adjustRTLAnimations();
        }
    }

    // Performance Optimizations
    function initPerformanceOptimizations() {
        // Lazy load non-critical content
        lazyLoadContent();
        
        // Optimize images
        optimizeImages();
        
        // Preload critical resources
        preloadCriticalResources();
        
        // Add performance monitoring
        monitorPerformance();
    }

    // Helper Functions
    function togglePanel(panel) {
        const content = panel.querySelector('.card-body, .list-group');
        const icon = panel.querySelector('.panel-collapse-icon');
        const header = panel.querySelector('.card-header');
        
        if (content && icon) {
            const isExpanded = header.getAttribute('aria-expanded') === 'true';
            
            if (isExpanded) {
                content.style.display = 'none';
                icon.style.transform = 'rotate(180deg)';
                header.setAttribute('aria-expanded', 'false');
            } else {
                content.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
                header.setAttribute('aria-expanded', 'true');
            }
            
            // Save state to localStorage
            const panelName = panel.getAttribute('menuItemName');
            if (panelName) {
                localStorage.setItem(`panel_${panelName}_collapsed`, isExpanded);
            }
        }
    }

    function animateProgressBar(progressBar) {
        const targetWidth = progressBar.dataset.targetWidth;
        if (targetWidth) {
            progressBar.style.transition = 'width 1s ease-out';
            progressBar.style.width = targetWidth;
        }
    }

    function createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(var(--bs-primary-rgb), 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            z-index: 1;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    function createLoadingIndicator() {
        const loader = document.createElement('div');
        loader.className = 'loading-indicator';
        loader.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
        loader.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(var(--bs-body-bg-rgb), 0.9);
            padding: 1rem;
            border-radius: 0.5rem;
            z-index: 10;
        `;
        return loader;
    }

    function addSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'visually-hidden-focusable btn btn-primary';
        skipLink.style.cssText = `
            position: absolute;
            top: 1rem;
            left: 1rem;
            z-index: 9999;
        `;
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    function enhanceFocusManagement() {
        // Add focus indicators
        const focusableElements = document.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])');
        
        focusableElements.forEach(element => {
            element.addEventListener('focus', function() {
                this.classList.add('widdx-focused');
            });
            
            element.addEventListener('blur', function() {
                this.classList.remove('widdx-focused');
            });
        });
    }

    function addLiveRegions() {
        // Add ARIA live region for dynamic content updates
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'visually-hidden';
        liveRegion.id = 'widdx-live-region';
        document.body.appendChild(liveRegion);
    }

    function enhanceKeyboardNavigation() {
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + H: Go to home
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                window.location.href = 'clientarea.php';
            }
            
            // Alt + S: Focus search
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input, [type="search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }

    function fixRTLIcons() {
        const icons = document.querySelectorAll('.fa-chevron-left, .fa-chevron-right');
        icons.forEach(icon => {
            if (icon.classList.contains('fa-chevron-left')) {
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-right');
            } else if (icon.classList.contains('fa-chevron-right')) {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
            }
        });
    }

    function adjustRTLAnimations() {
        // Adjust animations for RTL
        const style = document.createElement('style');
        style.textContent = `
            .widdx-rtl .progress-bar::before {
                animation-direction: reverse;
            }
        `;
        document.head.appendChild(style);
    }

    function lazyLoadContent() {
        // Implement lazy loading for non-critical content
        const lazyElements = document.querySelectorAll('[data-lazy]');
        
        if ('IntersectionObserver' in window) {
            const lazyObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        element.src = element.dataset.lazy;
                        element.classList.remove('lazy');
                        lazyObserver.unobserve(element);
                    }
                });
            });
            
            lazyElements.forEach(element => lazyObserver.observe(element));
        }
    }

    function optimizeImages() {
        // Add loading="lazy" to images below the fold
        const images = document.querySelectorAll('img:not([loading])');
        images.forEach((img, index) => {
            if (index > 2) { // Skip first 3 images
                img.loading = 'lazy';
            }
        });
    }

    function preloadCriticalResources() {
        // Preload critical CSS and JS
        const criticalResources = [
            '/templates/widdx/clientarea/assets/css/widdx-topbar.css',
            '/templates/widdx/clientarea/assets/js/widdx-topbar.js'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    function monitorPerformance() {
        // Basic performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData) {
                        console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                    }
                }, 0);
            });
        }
    }

    function logInteraction(action, target) {
        // Log user interactions for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', action, {
                'event_category': 'dashboard',
                'event_label': target
            });
        }
        
        // Console log for debugging
        console.log('Interaction:', action, target);
    }

    // Add CSS for ripple effect and focus indicators
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to { transform: scale(4); opacity: 0; }
        }
        
        .widdx-focused {
            outline: 2px solid var(--bs-primary);
            outline-offset: 2px;
        }
        
        .loading-indicator {
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    // Export functions for global use
    window.WiddxHome = {
        togglePanel: togglePanel,
        animateProgressBar: animateProgressBar,
        createRippleEffect: createRippleEffect,
        logInteraction: logInteraction
    };

})();
