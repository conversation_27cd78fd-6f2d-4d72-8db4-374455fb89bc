/*
 * <PERSON>TL Styles for WHMCS WIDDX Backend Theme
 */

/* RTL Variables */
:root {
  /* Font Family */
  --font-family-sans-serif: "Cairo", sans-serif;

  /* Direction specific variables */
  --float-start: right;
  --float-end: left;
  --text-align-start: right;
  --text-align-end: left;
}

/* Basic RTL Setup */
html {
  direction: rtl;
}

body,
header {
  direction: rtl;
  text-align: right;
}

/* Layout Adjustments */
.layout-wrapper.layout-content-navbar .layout-container {
  flex-direction: row-reverse;
}

.layout-menu-fixed .layout-navbar-full .layout-menu,
.layout-menu-fixed-offcanvas .layout-navbar-full .layout-menu {
  right: 0;
  left: auto;
}

.layout-menu-fixed .layout-page,
.layout-menu-fixed-offcanvas .layout-page {
  padding-right: 16.25rem;
  padding-left: 0;
}

.layout-navbar-fixed .layout-menu,
.layout-menu-fixed .layout-menu,
.layout-menu-fixed-offcanvas .layout-menu {
  right: 0;
  left: auto;
}

.layout-menu-toggle {
  right: 0;
  left: auto;
}

/* Fix for main layout container in RTL */
html[dir="rtl"] .layout-wrapper .layout-container {
  direction: rtl;
}

/* Fix for sidebar positioning in RTL */
html[dir="rtl"] .layout-menu {
  right: 0;
  left: auto;
  border-right: 0;
  border-left: 1px solid var(--border-color);
}

/* Fix for page content in RTL */
html[dir="rtl"] .layout-page {
  padding-right: 16.25rem;
  padding-left: 0;
}

/* Menu */
.menu-vertical {
  margin-right: 0;
  margin-left: auto;
}

.menu-inner {
  padding-right: 0;
}

.menu-inner .menu-item .menu-link {
  text-align: right;
}

.menu-inner .menu-item .menu-toggle::after {
  margin-right: auto;
  margin-left: 0;
  transform: rotate(180deg);
}

.menu-inner .menu-item .menu-toggle[aria-expanded="true"]::after {
  transform: rotate(270deg);
}

/* RTL specific menu toggle */
.rtl-toggle::after {
  transform: rotate(180deg) !important;
}

.rtl-toggle[aria-expanded="true"]::after {
  transform: rotate(270deg) !important;
}

.menu-inner .menu-sub {
  padding-right: 1.5rem;
  padding-left: 0;
}

/* Navbar */
.navbar-nav {
  padding-right: 0;
}

.navbar .navbar-nav .nav-link {
  text-align: right;
}

.dropdown-menu {
  text-align: right;
}

.dropdown-item {
  text-align: right;
}

/* RTL Navbar Fixes */
html[dir="rtl"] .layout-navbar {
  flex-direction: row-reverse;
}

html[dir="rtl"] .navbar-nav {
  padding-right: 0;
  margin-right: 0;
}

html[dir="rtl"] .navbar-nav .nav-item {
  margin-left: 0.5rem;
  margin-right: 0;
}

html[dir="rtl"] .layout-navbar .navbar-brand {
  margin-right: 0;
  margin-left: 1rem;
}

html[dir="rtl"] .layout-navbar .navbar-nav-right {
  margin-left: 0;
  margin-right: auto;
}

html[dir="rtl"] .layout-navbar .navbar-collapse {
  flex-direction: row-reverse;
}

html[dir="rtl"] .layout-navbar .layout-menu-toggle {
  margin-right: 0;
  margin-left: 1rem;
}

/* RTL Search Box */
html[dir="rtl"] .navbar-search .form-control {
  padding-right: 2.5rem;
  padding-left: 0.75rem;
  text-align: right;
}

html[dir="rtl"] .navbar-search .btn-search {
  right: 0;
  left: auto;
}

html[dir="rtl"] .navbar-search {
  margin-right: 0;
  margin-left: 1rem;
}

/* Position search box in RTL */
html[dir="rtl"] .layout-navbar .navbar-search-wrapper {
  margin-left: 0;
  margin-right: auto;
}

/* RTL Dropdown Menus */
html[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}

html[dir="rtl"] .dropdown-menu-end {
  left: 0 !important;
  right: auto !important;
}

html[dir="rtl"] .dropdown-toggle::after {
  margin-left: 0;
  margin-right: 0.255em;
}

/* RTL Navbar Icons */
html[dir="rtl"] .nav-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir="rtl"] .badge-number {
  right: auto;
  left: 0;
}

/* Cards */
.card-header {
  text-align: right;
}

/* RTL Card Fixes */
html[dir="rtl"] .card {
  text-align: right;
}

html[dir="rtl"] .card-header {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

html[dir="rtl"] .card-title {
  margin-right: 0;
  margin-left: auto;
}

html[dir="rtl"] .card-header .card-minimise {
  margin-left: 0;
  margin-right: auto;
}

/* RTL Content Fixes */
html[dir="rtl"] .container-xxl {
  direction: rtl;
}

html[dir="rtl"] .content-wrapper {
  direction: rtl;
}

/* RTL Dashboard Widgets */
html[dir="rtl"] .widget-card {
  text-align: right;
}

html[dir="rtl"] .widget-card .widget-icon {
  right: auto;
  left: 1rem;
}

/* Form Controls */
.form-label {
  text-align: right;
}

.form-check {
  padding-left: 0;
  padding-right: 1.5rem;
}

.form-check .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.5rem;
}

/* RTL Form Fixes */
html[dir="rtl"] .form-control {
  text-align: right;
  direction: rtl;
}

html[dir="rtl"] .form-select {
  text-align: right;
  padding-right: 0.75rem;
  padding-left: 2.25rem;
  background-position: left 0.75rem center;
}

html[dir="rtl"] .form-floating > label {
  right: 0;
  left: auto;
}

html[dir="rtl"] .was-validated .form-control:valid,
html[dir="rtl"] .form-control.is-valid {
  padding-right: 0.75rem;
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

html[dir="rtl"] .was-validated .form-control:invalid,
html[dir="rtl"] .form-control.is-invalid {
  padding-right: 0.75rem;
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

/* RTL Input Groups */
html[dir="rtl"] .input-group {
  direction: rtl;
}

html[dir="rtl"] .input-group > .form-control:not(:last-child),
html[dir="rtl"] .input-group > .custom-select:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

html[dir="rtl"] .input-group > .form-control:not(:first-child),
html[dir="rtl"] .input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

html[dir="rtl"] .input-group > .input-group-prepend > .btn,
html[dir="rtl"] .input-group > .input-group-prepend > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

html[dir="rtl"] .input-group > .input-group-append > .btn,
html[dir="rtl"] .input-group > .input-group-append > .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* RTL Buttons */
html[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
html[dir="rtl"] .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

html[dir="rtl"] .btn-group > .btn:not(:first-child),
html[dir="rtl"] .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Tables */
.table th,
.table td {
  text-align: right;
}

/* Alerts */
.alert-dismissible {
  padding-right: 1.25rem;
  padding-left: 4rem;
}

.alert-dismissible .close {
  left: 0;
  right: auto;
}

/* Modal */
.modal-header .close {
  margin: -1rem auto -1rem -1rem;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Pagination */
.pagination {
  padding-right: 0;
}

/* Icons */
.bx-chevron-right:before {
  content: "\ea64";
}

.bx-chevron-left:before {
  content: "\ea6e";
}

/* Margins and Paddings */
.ms-1 {
  margin-right: 0.25rem !important;
  margin-left: 0 !important;
}

.ms-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

.ms-3 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}

.ms-4 {
  margin-right: 1.5rem !important;
  margin-left: 0 !important;
}

.ms-5 {
  margin-right: 3rem !important;
  margin-left: 0 !important;
}

.me-1 {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}

.me-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

.me-3 {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}

.me-4 {
  margin-left: 1.5rem !important;
  margin-right: 0 !important;
}

.me-5 {
  margin-left: 3rem !important;
  margin-right: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.ps-1 {
  padding-right: 0.25rem !important;
  padding-left: 0 !important;
}

.ps-2 {
  padding-right: 0.5rem !important;
  padding-left: 0 !important;
}

.ps-3 {
  padding-right: 1rem !important;
  padding-left: 0 !important;
}

.ps-4 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important;
}

.ps-5 {
  padding-right: 3rem !important;
  padding-left: 0 !important;
}

.pe-1 {
  padding-left: 0.25rem !important;
  padding-right: 0 !important;
}

.pe-2 {
  padding-left: 0.5rem !important;
  padding-right: 0 !important;
}

.pe-3 {
  padding-left: 1rem !important;
  padding-right: 0 !important;
}

.pe-4 {
  padding-left: 1.5rem !important;
  padding-right: 0 !important;
}

.pe-5 {
  padding-left: 3rem !important;
  padding-right: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

/* Text Alignment */
.text-start {
  text-align: right !important;
}

.text-end {
  text-align: left !important;
}

/* Floats */
.float-start {
  float: right !important;
}

.float-end {
  float: left !important;
}

/* Dashboard Widgets */
.widget-title {
  text-align: right;
}

.widget-stats {
  text-align: right;
}

/* RTL-specific classes */
.rtl-layout .layout-menu {
  right: 0;
  left: auto;
}

.rtl-input-group .input-group-text {
  border-radius: 0 0.25rem 0.25rem 0;
}

.rtl-input-group .form-control:first-child {
  border-radius: 0.25rem 0 0 0.25rem;
}

/* Fix for RTL dropdowns */
.dropdown-menu-end {
  right: auto !important;
  left: 0 !important;
}

.dropdown-menu-start {
  left: auto !important;
  right: 0 !important;
}

/* RTL Navbar Brand and Logo */
html[dir="rtl"] .app-brand-logo {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir="rtl"] .app-brand-text {
  margin-right: 0;
  margin-left: auto;
}

html[dir="rtl"] .app-brand-link {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

/* RTL Navbar Specific Fixes */
html[dir="rtl"] .layout-navbar .container-xxl {
  flex-direction: row-reverse;
}

html[dir="rtl"] .layout-navbar .navbar-nav-right {
  margin-left: auto;
  margin-right: 0;
  justify-content: flex-start;
}

/* Fix for navbar icons distribution in RTL */
html[dir="rtl"] .navbar-nav.flex-row {
  
  justify-content: flex-start;
}

html[dir="rtl"] .navbar-nav.flex-row .nav-item {
  margin-left: 0;
  margin-right: 0;
}

html[dir="rtl"] .navbar-nav.flex-row .nav-link {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Fix for navbar icon alignment */
html[dir="rtl"] .navbar-nav.align-items-center {
  justify-content: flex-start;
}

/* Responsive Fixes */
@media (max-width: 1199.98px) {
  .layout-menu-fixed .layout-page,
  .layout-menu-fixed-offcanvas .layout-page {
    padding-right: 0;
  }

  .layout-menu {
    transform: translateX(100%);
  }

  .layout-menu-expanded .layout-menu {
    transform: translateX(0);
  }

  /* RTL specific mobile fixes */
  html[dir="rtl"] .layout-menu {
    right: auto;
    left: 0;
    transform: translateX(-100%);
  }

  html[dir="rtl"] .layout-menu-expanded .layout-menu {
    transform: translateX(0);
  }

  html[dir="rtl"] .layout-overlay {
    right: auto;
    left: 0;
  }
}

/* Fix for mobile menu toggle */
@media (max-width: 991.98px) {
  .layout-menu-toggle {
    right: 1rem;
    left: auto;
  }

  /* RTL mobile menu toggle */
  html[dir="rtl"] .layout-menu-toggle {
    right: auto;
    left: 1rem;
  }

  /* RTL mobile navbar */
  html[dir="rtl"] .layout-navbar .navbar-nav {
    flex-direction: row-reverse;
  }

  html[dir="rtl"] .navbar-brand.mx-auto {
    transform: translateX(50%) !important;
  }

  html[dir="rtl"] .layout-navbar .layout-menu-toggle {
    margin-right: 0;
    margin-left: auto;
  }

  html[dir="rtl"] .layout-navbar .navbar-nav-right {
    justify-content: flex-start;
  }

  /* RTL mobile search */
  html[dir="rtl"] .hide-on-pc {
    margin-left: 0;
    margin-right: auto;
  }

  html[dir="rtl"] .main-nav-search {
    margin-right: 0;
    margin-left: 1rem;
  }

  html[dir="rtl"] #search-modal .search-content-area {
    direction: rtl;
  }

  html[dir="rtl"] #search-modal .search-close {
    right: auto;
    left: 20px;
  }

  /* Fix for navbar icons spacing in mobile */
  html[dir="rtl"] .navbar-nav.flex-row.align-items-center {
    justify-content: flex-start;
  }

  html[dir="rtl"] .navbar-nav.flex-row.align-items-center .nav-item {
    margin: 0;
  }

  html[dir="rtl"] .navbar-nav.flex-row.align-items-center .nav-link {
    padding: 0.5rem;
  }

  /* Position navbar-nav-right to the left in RTL mobile */
  html[dir="rtl"] .navbar-nav-right {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
}

/* RTL Theme Toggle Button */
html[dir="rtl"] .ww-color-switch {
  right: auto;
  left: 20px;
}

/* RTL Navbar Theme Toggle */
html[dir="rtl"] .nav-item .ww-theme-toggle i {
  transform: scaleX(-1); /* Flip icons horizontally for RTL */
}

@media (max-width: 768px) {
  html[dir="rtl"] .ww-color-switch {
    right: auto;
    left: 15px;
  }
}

/* RTL Dashboard Specific Fixes */
html[dir="rtl"] .rtl-mode .dashboard-stats-card {
  text-align: right;
}

html[dir="rtl"] .rtl-mode .dashboard-stats-card .icon {
  right: auto;
  left: 20px;
}

html[dir="rtl"] .rtl-mode .dashboard-stats-card .stats-info {
  padding-right: 20px;
  padding-left: 60px;
}

html[dir="rtl"] .rtl-mode .dashboard-stats-card .stats-text {
  text-align: right;
}

/* RTL Search Box */
html[dir="rtl"] .rtl-search .form-control {
  padding-right: 2.5rem;
  padding-left: 0.75rem;
  text-align: right;
}

html[dir="rtl"] .rtl-search .btn-search {
  right: 0;
  left: auto;
}

/* RTL Navbar Search Specific */
html[dir="rtl"] .navbar-search .nav-item {
  flex-direction: row-reverse;
}

html[dir="rtl"] .navbar-search form {
  flex-direction: row-reverse;
}

html[dir="rtl"] .navbar-search .btn-search {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir="rtl"] #search-modal .search-content-area {
  direction: rtl;
  text-align: right;
}

html[dir="rtl"] #search-modal .search-close {
  right: auto;
  left: 20px;
}

html[dir="rtl"] #search-modal .search-form {
  flex-direction: row-reverse;
}

html[dir="rtl"] #search-modal .search-submit {
  right: auto;
  left: 15px;
}

/* RTL Navbar Icons Specific Fixes */
html[dir="rtl"] .navbar-nav .nav-link.nav-icon {
  padding: 0.5rem;
}

html[dir="rtl"] .navbar-nav .nav-item:last-child {
  margin-left: 0;
}

html[dir="rtl"] .navbar-nav .nav-item:first-child {
  margin-right: 0;
}

/* Fix for notification badge in RTL */
html[dir="rtl"] .badge-number {
  top: 0;
  right: auto;
  left: 0;
  transform: translate(-50%, -50%);
}

/* Fix for search icon in RTL */
html[dir="rtl"] .navbar-search-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* RTL Tabs */
html[dir="rtl"] .nav-tabs {
  padding-right: 0;
}

html[dir="rtl"] .nav-tabs .nav-item {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* RTL List Groups */
html[dir="rtl"] .list-group {
  padding-right: 0;
}

html[dir="rtl"] .list-group-item {
  text-align: right;
}