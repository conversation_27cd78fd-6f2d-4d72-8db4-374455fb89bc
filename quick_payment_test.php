<?php
/**
 * Quick Lahza Payment Gateway Test
 * 
 * Simple test to verify basic gateway functionality
 */

// Include the gateway file
require_once __DIR__ . '/modules/gateways/lahza.php';

echo "🚀 Quick Lahza Payment Gateway Test\n";
echo str_repeat("=", 50) . "\n";

// Test 1: Check if gateway functions exist
echo "1. Testing Gateway Function Availability:\n";

$functions = [
    'lahza_config',
    'lahza_link', 
    'lahza_capture',
    'lahza_refund'
];

foreach ($functions as $function) {
    if (function_exists($function)) {
        echo "   ✅ {$function}() - Available\n";
    } else {
        echo "   ❌ {$function}() - Missing\n";
    }
}

// Test 2: Check gateway configuration
echo "\n2. Testing Gateway Configuration:\n";

try {
    $config = lahza_config();
    
    if (is_array($config) && !empty($config)) {
        echo "   ✅ Configuration loaded successfully\n";
        echo "   📋 Available fields: " . count($config) . "\n";
        
        // Check required fields
        $requiredFields = ['publicKey', 'secretKey', 'testMode'];
        foreach ($requiredFields as $field) {
            if (isset($config[$field])) {
                echo "   ✅ {$field} field present\n";
            } else {
                echo "   ⚠️ {$field} field missing\n";
            }
        }
    } else {
        echo "   ❌ Configuration failed to load\n";
    }
} catch (Exception $e) {
    echo "   ❌ Configuration error: " . $e->getMessage() . "\n";
}

// Test 3: Test payment link generation
echo "\n3. Testing Payment Link Generation:\n";

try {
    $testParams = [
        'invoiceid' => '12345',
        'amount' => '100.00',
        'currency' => 'ILS',
        'clientdetails' => [
            'userid' => '1',
            'email' => '<EMAIL>',
            'firstname' => 'Test',
            'lastname' => 'User'
        ],
        'publicKey' => 'pk_test_0e9Ce7p2rYbMd5Gjwi1JSSfnUUmBs98eQ',
        'secretKey' => 'sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im',
        'testMode' => 'on',
        'systemurl' => 'http://localhost',
        'returnurl' => 'http://localhost/viewinvoice.php'
    ];
    
    $result = lahza_link($testParams);
    
    if (is_string($result) && !empty($result)) {
        echo "   ✅ Payment link generated successfully\n";
        echo "   🔗 Link length: " . strlen($result) . " characters\n";
        
        // Check if it contains expected elements
        if (strpos($result, 'form') !== false) {
            echo "   ✅ Contains HTML form\n";
        }
        if (strpos($result, 'lahza') !== false) {
            echo "   ✅ Contains Lahza references\n";
        }
    } else {
        echo "   ❌ Payment link generation failed\n";
        if (is_array($result)) {
            echo "   📋 Result: " . print_r($result, true) . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Payment link error: " . $e->getMessage() . "\n";
}

// Test 4: Test currency conversion
echo "\n4. Testing Currency Conversion:\n";

$testCurrencies = [
    ['currency' => 'ILS', 'amount' => 100.00, 'expected' => 10000],
    ['currency' => 'USD', 'amount' => 50.00, 'expected' => 5000],
    ['currency' => 'JOD', 'amount' => 25.000, 'expected' => 25000]
];

foreach ($testCurrencies as $test) {
    try {
        // Test the conversion logic (we'll need to extract this from the gateway)
        $converted = convertToSmallestUnit($test['amount'], $test['currency']);
        
        if ($converted === $test['expected']) {
            echo "   ✅ {$test['currency']} {$test['amount']} = {$converted} (correct)\n";
        } else {
            echo "   ❌ {$test['currency']} {$test['amount']} = {$converted} (expected {$test['expected']})\n";
        }
    } catch (Exception $e) {
        echo "   ⚠️ {$test['currency']} conversion error: " . $e->getMessage() . "\n";
    }
}

// Test 5: Check file permissions and directories
echo "\n5. Testing File System:\n";

$paths = [
    'modules/gateways/lahza',
    'modules/gateways/logs',
    'modules/gateways/callback'
];

foreach ($paths as $path) {
    if (is_dir($path)) {
        echo "   ✅ Directory exists: {$path}\n";
        if (is_writable($path)) {
            echo "   ✅ Directory writable: {$path}\n";
        } else {
            echo "   ⚠️ Directory not writable: {$path}\n";
        }
    } else {
        echo "   ❌ Directory missing: {$path}\n";
    }
}

// Helper function for currency conversion testing
function convertToSmallestUnit($amount, $currency) {
    switch ($currency) {
        case 'USD':
        case 'ILS':
            return (int)($amount * 100);
        case 'JOD':
            return (int)($amount * 1000);
        default:
            throw new Exception("Unsupported currency: {$currency}");
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "✅ Quick test completed!\n";
echo "📄 Check the results above for any issues.\n";
