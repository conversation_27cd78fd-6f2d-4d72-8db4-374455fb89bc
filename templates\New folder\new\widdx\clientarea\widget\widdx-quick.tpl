<!-- Quick Actions Modal -->
{assign var="quickActions" value=[
    [
        'title' => 'My Services',
        'icon' => 'server',
        'color' => 'primary',
        'url' => "clientarea.php?action=services",
        'description' => 'Manage your hosting services and control panels'
    ],
    [
        'title' => 'My Domains',
        'icon' => 'globe',
        'color' => 'success',
        'url' => "clientarea.php?action=domains",
        'description' => 'Manage your domain names'
    ],
    [
        'title' => 'Billing',
        'icon' => 'file-invoice-dollar',
        'color' => 'warning',
        'url' => "clientarea.php?action=invoices",
        'description' => 'View invoices and make payments'
    ],
    [
        'title' => 'Support Tickets',
        'icon' => 'ticket-alt',
        'color' => 'info',
        'url' => "supporttickets.php",
        'description' => 'Get help from our support team'
    ],
    [
        'title' => 'Email Services',
        'icon' => 'envelope',
        'color' => 'danger',
        'url' => "clientarea.php?action=emails",
        'description' => 'Manage your email accounts'
    ],
    [
        'title' => 'SSL Certificates',
        'icon' => 'shield-alt',
        'color' => 'secondary',
        'url' => "clientarea.php?action=services&type=ssl",
        'description' => 'Manage SSL/TLS certificates'
    ],
    [
        'title' => 'Downloads',
        'icon' => 'download',
        'color' => 'info',
        'url' => "clientarea.php?action=downloads",
        'description' => 'Access downloadable resources'
    ],
    [
        'title' => 'Announcements',
        'icon' => 'bullhorn',
        'color' => 'warning',
        'url' => "clientarea.php?action=announcements",
        'description' => 'Stay updated with latest news'
    ],
    [
        'title' => 'Affiliates',
        'icon' => 'users',
        'color' => 'success',
        'url' => "affiliates.php",
        'description' => 'Join our affiliate program'
    ],
    [
        'title' => 'Network Status',
        'icon' => 'signal',
        'color' => 'primary',
        'url' => "serverstatus.php",
        'description' => 'View system status and uptime'
    ],
    [
        'title' => 'SEO Tools',
        'icon' => 'chart-line',
        'color' => 'danger',
        'url' => "{$WEB_ROOT}/widdx-page.php?page=seo-analyzer",
        'description' => 'Analyze and improve SEO'
    ],
    [
        'title' => 'Whois Lookup',
        'icon' => 'search',
        'color' => 'secondary',
        'url' => "{$WEB_ROOT}/widdx-page.php?page=whois-checker",
        'description' => 'Check domain information'
    ]
]}

<div class="modal fade" id="quickActionsModal" tabindex="-1" aria-labelledby="quickActionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content border-0 shadow-lg">
      <div class="modal-header bg-white border-bottom">
        <h5 class="modal-title text-dark" id="quickActionsModalLabel">
          <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body py-4">
        <div class="row g-4">
          {foreach from=$quickActions item=action}
          <div class="col-6 col-md-4">
            <a href="{$action.url}" class="text-decoration-none">
              <div class="card h-100 border-0 shadow-sm quick-action-card">
                <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                  <i class="fas fa-{$action.icon} fa-2x text-{$action.color} mb-3"></i>
                  <h6 class="card-title mb-2 text-center">{$action.title}</h6>
                  <p class="card-text small text-muted text-center mb-0">{$action.description}</p>
                </div>
              </div>
            </a>
          </div>
          {/foreach}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
#quickActionsModal .modal-content {
  border-radius: 15px;
  overflow: hidden;
}

#quickActionsModal .modal-header {
  padding: 1.5rem;
}

#quickActionsModal .quick-action-card {
  transition: all 0.3s ease;
  border-radius: 10px;
}

#quickActionsModal .quick-action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

#quickActionsModal .quick-action-card i {
  transition: transform 0.3s ease;
}

#quickActionsModal .quick-action-card:hover i {
  transform: scale(1.1);
}

#quickActionsModal .card-title {
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

#quickActionsModal .quick-action-card:hover .card-title {
  color: #007bff;
}

#quickActionsModal .card-text {
  font-size: 0.85rem;
  line-height: 1.4;
}
</style>