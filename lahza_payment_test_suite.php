<?php
/**
 * Comprehensive Lahza Payment Gateway Test Suite
 * 
 * Tests all payment scenarios including successful payments, failures,
 * 3D Secure flows, currency handling, and edge cases.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

require_once __DIR__ . '/modules/gateways/lahza.php';
require_once __DIR__ . '/tests/lahza/test_config.php';

class LahzaPaymentTestSuite {
    
    private $config;
    private $testResults = [];
    private $passedTests = 0;
    private $failedTests = 0;
    private $startTime;
    
    // Test card numbers for different scenarios
    private $testCards = [
        'visa_success' => [
            'number' => '****************',
            'cvv' => '123',
            'expiry' => '12/25',
            'expected' => 'success'
        ],
        'visa_declined' => [
            'number' => '****************',
            'cvv' => '123', 
            'expiry' => '12/25',
            'expected' => 'declined'
        ],
        'visa_3ds_required' => [
            'number' => '****************',
            'cvv' => '123',
            'expiry' => '12/25',
            'expected' => '3ds_required'
        ],
        'mastercard_success' => [
            'number' => '****************',
            'cvv' => '123',
            'expiry' => '12/25',
            'expected' => 'success'
        ],
        'mastercard_declined' => [
            'number' => '5000000000000009',
            'cvv' => '123',
            'expiry' => '12/25',
            'expected' => 'declined'
        ],
        'insufficient_funds' => [
            'number' => '****************',
            'cvv' => '123',
            'expiry' => '12/25',
            'expected' => 'insufficient_funds'
        ],
        'expired_card' => [
            'number' => '****************',
            'cvv' => '123',
            'expiry' => '01/20',
            'expected' => 'expired_card'
        ]
    ];
    
    // Test currencies with different amounts
    private $testCurrencies = [
        'ILS' => [
            'amounts' => [1.00, 10.50, 100.00, 999.99],
            'multiplier' => 100,
            'symbol' => '₪'
        ],
        'USD' => [
            'amounts' => [1.00, 25.50, 100.00, 500.00],
            'multiplier' => 100,
            'symbol' => '$'
        ],
        'JOD' => [
            'amounts' => [1.000, 10.500, 50.000, 200.000],
            'multiplier' => 1000,
            'symbol' => 'د.ا'
        ]
    ];
    
    public function __construct() {
        $this->config = require __DIR__ . '/tests/lahza/test_config.php';
        $this->startTime = microtime(true);
        
        echo "🚀 Lahza Payment Gateway - Comprehensive Test Suite\n";
        echo str_repeat("=", 60) . "\n";
        echo "Test Started: " . date('Y-m-d H:i:s') . "\n";
        echo str_repeat("=", 60) . "\n\n";
    }
    
    /**
     * Run all payment tests
     */
    public function runAllTests() {
        // Test 1: Successful Payment Scenarios
        $this->testSuccessfulPayments();
        
        // Test 2: Payment Failure Scenarios  
        $this->testPaymentFailures();
        
        // Test 3: Currency and Amount Validation
        $this->testCurrencyHandling();
        
        // Test 4: 3D Secure Authentication
        $this->test3DSecureFlow();
        
        // Test 5: Edge Cases and Error Handling
        $this->testEdgeCases();
        
        // Generate final report
        $this->generateReport();
    }
    
    /**
     * Test successful payment scenarios
     */
    private function testSuccessfulPayments() {
        echo "💳 Testing Successful Payment Scenarios\n";
        echo str_repeat("-", 40) . "\n";
        
        // Test Visa success
        $this->runPaymentTest(
            'Visa Successful Payment',
            $this->testCards['visa_success'],
            'ILS',
            100.00,
            'success'
        );
        
        // Test Mastercard success
        $this->runPaymentTest(
            'Mastercard Successful Payment',
            $this->testCards['mastercard_success'],
            'USD',
            50.00,
            'success'
        );
        
        // Test different amounts
        foreach ([1.00, 10.50, 100.00, 999.99] as $amount) {
            $this->runPaymentTest(
                "Visa Payment - Amount {$amount} ILS",
                $this->testCards['visa_success'],
                'ILS',
                $amount,
                'success'
            );
        }
        
        echo "\n";
    }
    
    /**
     * Test payment failure scenarios
     */
    private function testPaymentFailures() {
        echo "❌ Testing Payment Failure Scenarios\n";
        echo str_repeat("-", 40) . "\n";
        
        // Test declined card
        $this->runPaymentTest(
            'Visa Declined Payment',
            $this->testCards['visa_declined'],
            'ILS',
            100.00,
            'declined'
        );
        
        // Test insufficient funds
        $this->runPaymentTest(
            'Insufficient Funds',
            $this->testCards['insufficient_funds'],
            'USD',
            100.00,
            'insufficient_funds'
        );
        
        // Test expired card
        $this->runPaymentTest(
            'Expired Card',
            $this->testCards['expired_card'],
            'ILS',
            50.00,
            'expired_card'
        );
        
        echo "\n";
    }
    
    /**
     * Test currency handling and conversion
     */
    private function testCurrencyHandling() {
        echo "💱 Testing Currency Handling\n";
        echo str_repeat("-", 40) . "\n";
        
        foreach ($this->testCurrencies as $currency => $config) {
            foreach ($config['amounts'] as $amount) {
                $this->runCurrencyTest($currency, $amount, $config['multiplier']);
            }
        }
        
        // Test unsupported currency
        $this->runInvalidCurrencyTest('EUR', 100.00);
        
        echo "\n";
    }
    
    /**
     * Test 3D Secure authentication flow
     */
    private function test3DSecureFlow() {
        echo "🔐 Testing 3D Secure Authentication\n";
        echo str_repeat("-", 40) . "\n";
        
        // Test 3DS required
        $this->runPaymentTest(
            '3D Secure Required',
            $this->testCards['visa_3ds_required'],
            'ILS',
            100.00,
            '3ds_required'
        );
        
        // Test 3DS authentication success
        $this->run3DSAuthenticationTest('success');
        
        // Test 3DS authentication failure
        $this->run3DSAuthenticationTest('failure');
        
        echo "\n";
    }
    
    /**
     * Test edge cases and error handling
     */
    private function testEdgeCases() {
        echo "⚠️ Testing Edge Cases\n";
        echo str_repeat("-", 40) . "\n";
        
        // Test invalid amounts
        $this->runInvalidAmountTest(0);
        $this->runInvalidAmountTest(-10.00);
        $this->runInvalidAmountTest('invalid');

        // Test invalid email
        $this->runInvalidEmailTest('invalid-email');

        // Test missing required fields
        $this->runMissingFieldTest('email');
        $this->runMissingFieldTest('amount');
        
        echo "\n";
    }
    
    /**
     * Run individual payment test
     */
    private function runPaymentTest($testName, $cardData, $currency, $amount, $expectedResult) {
        $testStart = microtime(true);
        
        try {
            // Prepare payment parameters
            $params = $this->preparePaymentParams($cardData, $currency, $amount);
            
            // Call payment gateway
            $result = lahza_capture($params);
            
            // Validate result
            $success = $this->validatePaymentResult($result, $expectedResult);
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Run currency conversion test
     */
    private function runCurrencyTest($currency, $amount, $multiplier) {
        $testName = "Currency Test - {$amount} {$currency}";
        $testStart = microtime(true);
        
        try {
            // Test currency conversion logic
            $convertedAmount = $this->convertToSmallestUnit($amount, $currency);
            $expectedAmount = (int)($amount * $multiplier);
            
            $success = ($convertedAmount === $expectedAmount);
            
            if (!$success) {
                $error = "Expected {$expectedAmount}, got {$convertedAmount}";
                $this->recordTestResult($testName, false, microtime(true) - $testStart, $error);
            } else {
                $this->recordTestResult($testName, true, microtime(true) - $testStart);
            }
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Convert amount to smallest currency unit
     */
    private function convertToSmallestUnit($amount, $currency) {
        switch ($currency) {
            case 'USD':
            case 'ILS':
                return (int)($amount * 100);
            case 'JOD':
                return (int)($amount * 1000);
            default:
                throw new Exception("Unsupported currency: {$currency}");
        }
    }
    
    /**
     * Prepare payment parameters for testing
     */
    private function preparePaymentParams($cardData, $currency, $amount) {
        return [
            'invoiceid' => rand(1000, 9999),
            'amount' => $amount,
            'currency' => $currency,
            'clientdetails' => [
                'userid' => rand(100, 999),
                'email' => '<EMAIL>',
                'firstname' => 'Test',
                'lastname' => 'User'
            ],
            'publicKey' => $this->config['test_public_key'],
            'secretKey' => $this->config['test_secret_key'],
            'testMode' => 'on',
            'systemurl' => $this->config['whmcs']['system_url'],
            'returnurl' => $this->config['whmcs']['system_url'] . '/viewinvoice.php'
        ];
    }
    
    /**
     * Validate payment result
     */
    private function validatePaymentResult($result, $expectedResult) {
        switch ($expectedResult) {
            case 'success':
                return isset($result['status']) && $result['status'] === 'success';
            case 'declined':
            case 'insufficient_funds':
            case 'expired_card':
                return isset($result['status']) && $result['status'] === 'error';
            case '3ds_required':
                return isset($result['status']) && $result['status'] === '3ds_required';
            default:
                return false;
        }
    }
    
    /**
     * Record test result
     */
    private function recordTestResult($testName, $success, $duration, $error = null) {
        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'duration' => round($duration * 1000, 2), // Convert to milliseconds
            'error' => $error
        ];
        
        if ($success) {
            $this->passedTests++;
            echo "✅ {$testName} - PASSED (" . round($duration * 1000, 2) . "ms)\n";
        } else {
            $this->failedTests++;
            echo "❌ {$testName} - FAILED (" . round($duration * 1000, 2) . "ms)";
            if ($error) {
                echo " - {$error}";
            }
            echo "\n";
        }
    }
    
    /**
     * Generate final test report
     */
    private function generateReport() {
        $totalTime = microtime(true) - $this->startTime;
        $totalTests = $this->passedTests + $this->failedTests;
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 TEST RESULTS SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total Tests: {$totalTests}\n";
        echo "✅ Passed: {$this->passedTests}\n";
        echo "❌ Failed: {$this->failedTests}\n";
        echo "Success Rate: " . round(($this->passedTests / $totalTests) * 100, 2) . "%\n";
        echo "Total Duration: " . round($totalTime, 2) . " seconds\n";
        echo "Average Test Time: " . round(($totalTime / $totalTests) * 1000, 2) . "ms\n";
        
        if ($this->failedTests > 0) {
            echo "\n❌ FAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "- {$result['name']}";
                    if ($result['error']) {
                        echo " ({$result['error']})";
                    }
                    echo "\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
        
        // Save detailed report to file
        $this->saveDetailedReport();
    }
    
    /**
     * Save detailed report to file
     */
    private function saveDetailedReport() {
        $reportFile = __DIR__ . '/lahza_test_report_' . date('Ymd_His') . '.json';

        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => [
                'total_tests' => $this->passedTests + $this->failedTests,
                'passed' => $this->passedTests,
                'failed' => $this->failedTests,
                'success_rate' => round(($this->passedTests / ($this->passedTests + $this->failedTests)) * 100, 2),
                'total_duration' => microtime(true) - $this->startTime
            ],
            'test_results' => $this->testResults
        ];

        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT));
        echo "📄 Detailed report saved to: {$reportFile}\n";
    }

    /**
     * Test invalid currency
     */
    private function runInvalidCurrencyTest($currency, $amount) {
        $testName = "Invalid Currency Test - {$currency}";
        $testStart = microtime(true);

        try {
            $params = $this->preparePaymentParams($this->testCards['visa_success'], $currency, $amount);
            $result = lahza_capture($params);

            // Should fail with invalid currency
            $success = isset($result['status']) && $result['status'] === 'error';
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);

        } catch (Exception $e) {
            // Exception expected for invalid currency
            $this->recordTestResult($testName, true, microtime(true) - $testStart);
        }
    }

    /**
     * Test 3DS authentication
     */
    private function run3DSAuthenticationTest($scenario) {
        $testName = "3DS Authentication - {$scenario}";
        $testStart = microtime(true);

        // Simulate 3DS authentication response
        $authData = [
            'transaction_id' => 'test_3ds_' . time(),
            'authentication_status' => $scenario === 'success' ? 'Y' : 'N',
            'eci' => $scenario === 'success' ? '05' : '07',
            'cavv' => $scenario === 'success' ? 'AAABBEg0VhI0VniQEjRWAAAAAAA=' : null
        ];

        try {
            $result = lahza_process3DSecureResponse($authData);
            $success = ($scenario === 'success' && $result['status'] === 'authenticated') ||
                      ($scenario === 'failure' && $result['status'] === 'failed');

            $this->recordTestResult($testName, $success, microtime(true) - $testStart);

        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }

    /**
     * Test invalid amount
     */
    private function runInvalidAmountTest($amount) {
        $testName = "Invalid Amount Test - {$amount}";
        $testStart = microtime(true);

        try {
            $params = $this->preparePaymentParams($this->testCards['visa_success'], 'ILS', $amount);
            $result = lahza_capture($params);

            // Should fail with invalid amount
            $success = isset($result['status']) && $result['status'] === 'error';
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);

        } catch (Exception $e) {
            // Exception expected for invalid amount
            $this->recordTestResult($testName, true, microtime(true) - $testStart);
        }
    }

    /**
     * Test invalid email
     */
    private function runInvalidEmailTest($email) {
        $testName = "Invalid Email Test - {$email}";
        $testStart = microtime(true);

        try {
            $params = $this->preparePaymentParams($this->testCards['visa_success'], 'ILS', 100.00);
            $params['clientdetails']['email'] = $email;
            $result = lahza_capture($params);

            // Should fail with invalid email
            $success = isset($result['status']) && $result['status'] === 'error';
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);

        } catch (Exception $e) {
            // Exception expected for invalid email
            $this->recordTestResult($testName, true, microtime(true) - $testStart);
        }
    }

    /**
     * Test missing required field
     */
    private function runMissingFieldTest($field) {
        $testName = "Missing Field Test - {$field}";
        $testStart = microtime(true);

        try {
            $params = $this->preparePaymentParams($this->testCards['visa_success'], 'ILS', 100.00);

            // Remove the required field
            if ($field === 'email') {
                unset($params['clientdetails']['email']);
            } elseif ($field === 'amount') {
                unset($params['amount']);
            }

            $result = lahza_capture($params);

            // Should fail with missing field
            $success = isset($result['status']) && $result['status'] === 'error';
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);

        } catch (Exception $e) {
            // Exception expected for missing field
            $this->recordTestResult($testName, true, microtime(true) - $testStart);
        }
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $testSuite = new LahzaPaymentTestSuite();
    $testSuite->runAllTests();
}
