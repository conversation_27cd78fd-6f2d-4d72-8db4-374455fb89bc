<!-- Mobile Search Modal -->
<div id="searchModal"
     class="modal fade search-modal"
     tabindex="-1"
     aria-labelledby="searchModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-sm-down modal-dialog-centered">
        <div class="modal-content search-modal-content">
            <div class="modal-header search-modal-header">
                <h5 class="modal-title" id="searchModalLabel">
                    <i class="fas fa-search me-2" aria-hidden="true"></i>
                    {lang key='search.title'}
                </h5>
                <button type="button"
                        class="btn-close search-close-btn"
                        data-bs-dismiss="modal"
                        aria-label="{lang key='close'}">
                </button>
            </div>
            <div class="modal-body search-modal-body">
                <form method="post"
                      action="{routePath('knowledgebase-search')}"
                      class="search-form-mobile"
                      role="search">
                    <div class="search-input-group-mobile">
                        <input type="text"
                               name="search"
                               class="form-control search-input-mobile"
                               placeholder="{lang key='searchOurKnowledgebase'}..."
                               aria-label="{lang key='search.placeholder'}"
                               autocomplete="off"
                               maxlength="100"
                               autofocus>
                        <button type="submit"
                                class="btn btn-primary search-submit-mobile"
                                aria-label="{lang key='search.submit'}">
                            <i class="fas fa-search" aria-hidden="true"></i>
                        </button>
                    </div>
                </form>
                <div class="search-hint-mobile">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1" aria-hidden="true"></i>
                        {lang key='search.hint'}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Mobile Search Modal Styles */
.search-modal .modal-content {
  border-radius: 1rem;
  border: none;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.search-modal-header {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, var(--bs-primary)));
  color: white;
  border-radius: 1rem 1rem 0 0;
  padding: 1.5rem;
  border-bottom: none;
}

.search-modal-header .modal-title {
  font-weight: 600;
  font-size: 1.1rem;
}

.search-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.search-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.search-modal-body {
  padding: 2rem 1.5rem;
}

.search-form-mobile {
  margin-bottom: 1.5rem;
}

.search-input-group-mobile {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.search-input-mobile {
  flex: 1;
  padding: 0.875rem 1rem;
  font-size: 1rem;
  border: 2px solid rgba(var(--bs-border-color-rgb), 0.3);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.search-input-mobile:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.search-submit-mobile {
  padding: 0.875rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 60px;
}

.search-submit-mobile:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(var(--bs-primary-rgb), 0.3);
}

.search-hint-mobile {
  text-align: center;
  padding: 1rem;
  background-color: rgba(var(--bs-info-rgb), 0.1);
  border-radius: 0.5rem;
  border: 1px solid rgba(var(--bs-info-rgb), 0.2);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .search-modal .modal-content {
  background-color: var(--bs-dark);
}

[data-bs-theme="dark"] .search-modal-header {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, var(--bs-primary)));
}

[data-bs-theme="dark"] .search-input-mobile {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--bs-gray-100);
}

[data-bs-theme="dark"] .search-input-mobile:focus {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: var(--bs-primary);
}

[data-bs-theme="dark"] .search-hint-mobile {
  background-color: rgba(var(--bs-info-rgb), 0.15);
  border-color: rgba(var(--bs-info-rgb), 0.3);
}

/* Animation for modal */
.search-modal.fade .modal-dialog {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.search-modal.show .modal-dialog {
  transform: scale(1);
}

/* Responsive adjustments */
@media (max-width: 575.98px) {
  .search-modal-body {
    padding: 1.5rem 1rem;
  }

  .search-input-group-mobile {
    flex-direction: column;
    gap: 1rem;
  }

  .search-submit-mobile {
    width: 100%;
    padding: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .search-close-btn,
  .search-submit-mobile,
  .search-modal.fade .modal-dialog {
    transition: none;
  }
}
</style>
