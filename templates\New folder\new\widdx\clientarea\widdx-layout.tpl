<!DOCTYPE html>
<html lang="{if $language}{$language}{else}en{/if}" class="light-style layout-menu-fixed" dir="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}rtl{else}ltr{/if}" data-bs-theme="light"
  data-assets-path="{$WEB_ROOT}/templates/{$template}/clientarea/assets/" data-template="vertical-menu-template-free">
<!-- Immediate theme application -->
<script>
  (function() {
    var savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      document.documentElement.setAttribute('data-bs-theme', savedTheme);
    }
  })();
</script>

<head>
  <meta charset="{$charset}" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>{if $kbarticle.title}{$kbarticle.title} - {/if}{if $pagetitle}{$pagetitle} - {/if}{$companyname}</title>
  <meta name="description" content="WIDDX Backend Dashboard" />

  <!-- CSS Variables (Load first for performance) -->
  <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/variables.css" rel="stylesheet">

  <!-- CSRF Token Meta Tag -->
  <meta name="csrf-token" content="{$token}">

  <!-- PWA -->
  <link rel="manifest" href="{$WEB_ROOT}/templates/{$template}/clientarea/manifest.json">
  <meta name="theme-color" content="#696cff">
  <link rel="apple-touch-icon" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/img/icons/icon-192x192.png">

  {if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
    <link href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/rtl/bootstrap-rtl.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/rtl/widdx-rtl.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/rtl/backend-backend-rtl.css" rel="stylesheet">
  {/if}

  <!-- Favicon -->
  <link rel="icon" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/favicon.ico" type="image/x-icon">

  <!-- CSS Assets -->
  <link href="{assetPath file='all.min.css'}?v={$versionHash}" rel="stylesheet">
  <link href="{assetPath file='theme.min.css'}?v={$versionHash}" rel="stylesheet">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
    rel="stylesheet" />

  <!-- Icons -->
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/fonts/boxicons.css" />
  <!-- SEO -->
  {include file="$template/frontend/inc/widdx-head-seo.tpl"}
  <!-- Core CSS -->
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/css/core.css"
    class="template-customizer-core-css" />
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/css/theme-default.css"
    class="template-customizer-theme-css" />

  <!-- Vendors CSS -->
  <link rel="stylesheet"
    href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />
  <link rel="stylesheet"
    href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/libs/apex-charts/apex-charts.css" />
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/widdx.css" />
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/return-to-admin.css" />
  <!-- Theme System -->
  <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/clientarea/assets/css/theme-system.css" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>




  <!-- Template customizer & Theme config files -->
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/config.js"></script>


  <script>
    var csrfToken = '{$token}',
    markdownGuide = '{lang|addslashes key="markdown.title"}',
    locale = '{if !empty($mdeLocale)}{$mdeLocale}{else}en{/if}',
    saved = '{lang|addslashes key="markdown.saved"}',
    saving = '{lang|addslashes key="markdown.saving"}',
    whmcsBaseUrl = "{\WHMCS\Utility\Environment\WebHelper::getBaseUrl()}",
    {if $captcha}{$captcha->getPageJs()}{/if}
  </script>
  <script src="{assetPath file='scripts.min.js'}?v={$versionHash}"></script>
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/vendor/js/menu.js"></script>
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/main.js"></script>
  <!-- Menu Toggle System -->
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/menu-toggle.js"></script>
  <!-- Theme System -->
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/theme-system.js"></script>
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/theme-debug.js"></script>

  {if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
  <!-- RTL Support -->
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/rtl-support.js"></script>
  <script src="{$WEB_ROOT}/templates/{$template}/clientarea/assets/js/backend-rtl-support.js"></script>
  {/if}

  <script>
    // Register Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('{$WEB_ROOT}/templates/{$template}/clientarea/service-worker.js')
        .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });
      });
    }
  </script>

  {$headoutput}
</head>

<body data-bs-theme="light">
<script>
  // Ensure body has the same theme as html
  document.body.setAttribute('data-bs-theme', document.documentElement.getAttribute('data-bs-theme'));
</script>
  {$headeroutput}
  <script>
    var clientEmail = '{$client.email}';
  </script>

  <!-- Floating Theme Toggle Button (visible only on mobile) -->
  {include file="$template/frontend/inc/widdx-light-dark-switcher.tpl"}

  <!-- Layout wrapper -->
  <div class="layout-wrapper layout-content-navbar">
    <div class="layout-container">

      <!-- Menu -->
      {include file="$template/clientarea/partial/widdx-menu.tpl"}
      <!-- / Menu -->

      <!-- Layout container -->
      <div class="layout-page">

        <!-- Navbar -->
        {include file="$template/clientarea/partial/widdx-topbar.tpl"}
        <!-- / Navbar -->

        {include file="$template/includes/network-issues-notifications.tpl"}
        {include file="$template/includes/validateuser.tpl"}
        {include file="$template/includes/verifyemail.tpl"}

        <!-- Content wrapper -->
        <div class="content-wrapper">

          <!-- Content -->
          <div class="container-xxl flex-grow-1 container-p-y">
            <div class="{if !$skipMainBodyContainer}row{/if}">

              {if !$inShoppingCart && ($primarySidebar->hasChildren() || $secondarySidebar->hasChildren())}
                <div class="col-lg-4 col-xl-3">
                  <div class="sidebar">
                    {include file="$template/clientarea/inc/widdx-sidebar.tpl" sidebar=$primarySidebar}
                  </div>
                  {if !$inShoppingCart && $secondarySidebar->hasChildren()}
                    <div class="d-none d-lg-block sidebar">
                      {include file="$template/clientarea/inc/widdx-sidebar.tpl" sidebar=$secondarySidebar}
                    </div>
                  {/if}
                </div>
              {/if}

              <div
class="{if !$inShoppingCart && ($primarySidebar->hasChildren() || $secondarySidebar->hasChildren())}col-lg-8 col-xl-9{else}col-12{/if} primary-content">
