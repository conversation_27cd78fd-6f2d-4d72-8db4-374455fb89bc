<svg class="animated" id="freepik_stories-market-launch" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"><style>svg#freepik_stories-market-launch:not(.animated) .animable {opacity: 0;}svg#freepik_stories-market-launch.animated #freepik--background-simple--inject-16 {animation: 1.5s Infinite  linear wind;animation-delay: 0s;}svg#freepik_stories-market-launch.animated #freepik--Clouds--inject-16 {animation: 3s Infinite  linear floating;animation-delay: 0s;}svg#freepik_stories-market-launch.animated #freepik--Rocket--inject-16 {animation: 1.5s Infinite  linear floating;animation-delay: 0s;}svg#freepik_stories-market-launch.animated #freepik--Character--inject-16 {animation: 1.5s Infinite  linear wind;animation-delay: 0s;}            @keyframes wind {                0% {                    transform: rotate( 0deg );                }                25% {                    transform: rotate( 1deg );                }                75% {                    transform: rotate( -1deg );                }            }                    @keyframes floating {                0% {                    opacity: 1;                    transform: translateY(0px);                }                50% {                    transform: translateY(-10px);                }                100% {                    opacity: 1;                    transform: translateY(0px);                }            }        </style><g id="freepik--background-simple--inject-16" class="animable animator-active" style="transform-origin: 241.275px 221.996px;"><path d="M46.26,232s.38,70.53,57.86,118.28,138.79,52.17,204.57,57.48,117.39-31.4,126.53-87.8-40.77-73.63-66.36-138.29S346.26,95.9,289.51,55.6s-147.82-17.18-197,50S46.26,232,46.26,232Z" style="fill: rgb(186, 104, 200); transform-origin: 241.275px 221.996px;" id="elntpymzq7bhp" class="animable"></path><g id="el8rqpok3y4s"><path d="M46.26,232s.38,70.53,57.86,118.28,138.79,52.17,204.57,57.48,117.39-31.4,126.53-87.8-40.77-73.63-66.36-138.29S346.26,95.9,289.51,55.6s-147.82-17.18-197,50S46.26,232,46.26,232Z" style="fill: rgb(255, 255, 255); opacity: 0.7; transform-origin: 241.275px 221.996px;" class="animable"></path></g></g><g id="freepik--Clouds--inject-16" class="animable" style="transform-origin: 246.91px 169.583px;"><path d="M383.28,160.79a12.88,12.88,0,0,0-2.67-.91c-5.39-1.29-11.69-2.3-11.69-2.3s9.45-9.55,5.85-13.71-9.88,2.24-9.88,2.24,6.29-17.29-2.69-19.85-18.86,16.65-18.86,16.65-.45-7.37-4.94-6.41-5.39,9.61-5.39,9.61-6.74-3.84-8.53-.32,2.24,10.56,2.24,10.56-7.63-4.48-8.53-.32a17.26,17.26,0,0,0-.36,4.76Z" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 350.538px 143.408px;" id="el3wz86kf6azh" class="animable"></path><path d="M445.5,160.79,428,158.46a28.93,28.93,0,0,0-15.85-6l-2-.13s4.75-5.7,1.9-18.06-19-1.9-19-1.9,11.85-11.26,9.95-21.72-18.5-2-18.5-2,1.94-28.2-19-27.25-24.76,33.9-24.76,33.9-14.73-17.91-24.72,4.76a48.74,48.74,0,0,0-3.26,10.48l-.42-.42s-7.83-9.54-14-2.72-5.45,17-5.45,17-9.54-3.4-11.92.69,3.06,8.85,3.06,8.85-5.11-2.38-8.86,1c-2.35,2.13-4.82,4.39-6.35,5.79Z" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 357.16px 121.083px;" id="el0lqytfp27sql" class="animable"></path><path d="M162.7,257.77a13.45,13.45,0,0,0-2.67-.92c-5.39-1.28-11.69-2.29-11.69-2.29s9.45-9.55,5.85-13.71-9.88,2.24-9.88,2.24,6.29-17.29-2.69-19.85-18.86,16.65-18.86,16.65-.45-7.37-4.94-6.41-5.39,9.61-5.39,9.61-6.74-3.84-8.53-.32,2.24,10.56,2.24,10.56-7.63-4.48-8.53-.32a17.26,17.26,0,0,0-.36,4.76Z" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 129.958px 240.388px;" id="el16usv11lzm5" class="animable"></path><path d="M224.92,257.77l-17.47-2.33a28.93,28.93,0,0,0-15.85-5.95l-2-.13s4.75-5.7,1.9-18.06-19-1.9-19-1.9,11.85-11.26,9.95-21.72-18.5-2.05-18.5-2.05,1.94-28.21-19-27.25-24.76,33.9-24.76,33.9S105.48,194.37,95.49,217a48.74,48.74,0,0,0-3.26,10.48l-.42-.42s-7.83-9.54-14-2.72-5.45,17-5.45,17-9.54-3.4-11.93.68S63.53,251,63.53,251s-5.11-2.38-8.86,1c-2.35,2.13-4.82,4.39-6.35,5.79Z" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 136.62px 218.073px;" id="eli4el5dgwusl" class="animable"></path></g><g id="freepik--Rocket--inject-16" class="animable" style="transform-origin: 253.54px 230.8px;"><path d="M440.15,367.85a34.25,34.25,0,0,0-22,8,24.3,24.3,0,0,0-33.81-2.14,28.71,28.71,0,0,0-43.61,7.8,11.54,11.54,0,0,0-17,10.18,11.94,11.94,0,0,0,.08,1.35,19.28,19.28,0,0,0-2.16-.12,18.68,18.68,0,0,0-12.14,4.46,15.84,15.84,0,0,0-10.33-3.82,14.72,14.72,0,0,0-1.49.08V237.24a19.65,19.65,0,0,1,2.81-4h0a9.3,9.3,0,1,0-12.36,0l0,0a19.74,19.74,0,0,1,2.7,3.79V395.93a16.52,16.52,0,0,0-3.41,2.82,22.5,22.5,0,0,0-26.06-1.95,22.4,22.4,0,0,0-3.44-1.69V250.54c1.52-6,6.3-11,6.3-11v0a14.7,14.7,0,1,0-19.55,0l0,.05s5,5.26,6.42,11.52V393.58c-.44,0-.89,0-1.35,0a22.39,22.39,0,0,0-14.39,5.21,15.85,15.85,0,0,0-18.15-3.86V237.24a19.65,19.65,0,0,1,2.81-4h0a9.3,9.3,0,1,0-12.36,0l0,0a19.74,19.74,0,0,1,2.7,3.79V395.33a18.69,18.69,0,0,0-21.72,2.37,5.83,5.83,0,0,0,.11-1.12,5.65,5.65,0,0,0-5.65-5.65,5.56,5.56,0,0,0-1.29.16,10,10,0,0,0-14.54-7.91,28.73,28.73,0,0,0-44.51-9.49,24.3,24.3,0,0,0-33.81,2.14,34.33,34.33,0,0,0-56.11,30.82H474.25a35.79,35.79,0,0,0,.29-4.42A34.39,34.39,0,0,0,440.15,367.85Z" style="fill: rgb(194, 194, 194); transform-origin: 253.54px 310.255px;" id="eloayx73fav5d" class="animable"></path><path d="M217.65,224.46c0-2.14-1.73-5.54-3.88-5.54s-3.88,3.4-3.88,5.54,3.3,6.42,3.88,6.42S217.65,226.6,217.65,224.46Z" style="fill: rgb(186, 104, 200); transform-origin: 213.77px 224.9px;" id="el8ji0w8mnivp" class="animable"></path><path d="M298.21,224.46c0-2.14-1.73-5.54-3.88-5.54s-3.88,3.4-3.88,5.54,3.3,6.42,3.88,6.42S298.21,226.6,298.21,224.46Z" style="fill: rgb(186, 104, 200); transform-origin: 294.33px 224.9px;" id="elfic6xrpwuew" class="animable"></path><path d="M214.22,198.93l27.36-24.08v20.43l-25.54,12Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 227.9px 191.065px;" id="elw71f55gdh2" class="animable"></path><path d="M219.73,218.75H207.85l-2.51-46.63a4.88,4.88,0,0,1,0-1c.41-3.85,4-16.47,8.44-16.47h0c4.41,0,8,12.62,8.45,16.47a5.68,5.68,0,0,1,0,1Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 213.783px 186.7px;" id="elxribftopoka" class="animable"></path><path d="M207,164.36h13.6c-1.54-4.68-4-9.72-6.8-9.72S208.54,159.68,207,164.36Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 213.8px 159.5px;" id="el5zd3g9wb54w" class="animable"></path><polygon points="207.32 208.91 207.85 218.75 219.73 218.75 220.26 208.91 207.32 208.91" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 213.79px 213.83px;" id="eltcw6ase19g" class="animable"></polygon><path d="M294.39,198.93,267,174.85v20.43l25.54,12Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 280.695px 191.065px;" id="elh6zv3rag44j" class="animable"></path><path d="M288.88,218.75h11.88l2.51-46.63a4.88,4.88,0,0,0,0-1c-.41-3.85-4-16.47-8.44-16.47h0c-4.41,0-8,12.62-8.45,16.47a5.68,5.68,0,0,0,0,1Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 294.827px 186.7px;" id="elglm4x871m0s" class="animable"></path><polygon points="301.29 208.91 300.76 218.75 288.88 218.75 288.35 208.91 301.29 208.91" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 294.82px 213.83px;" id="elkzt3i7oxxo" class="animable"></polygon><path d="M288,164.36h13.6c-1.55-4.68-4-9.72-6.8-9.72S289.56,159.68,288,164.36Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 294.8px 159.5px;" id="elr4olh1mwce" class="animable"></path><path d="M260.58,225.63a6.14,6.14,0,0,0-12.27,0c0,3.39,5.21,10.15,6.13,10.15C255.16,235.78,260.58,229,260.58,225.63Z" style="fill: rgb(186, 104, 200); transform-origin: 254.445px 227.759px;" id="el4abb0vrm60i" class="animable"></path><path d="M270,202.33H238.93l-6.58-100.92a13.8,13.8,0,0,1,0-2.65c1.1-10.1,10.59-43.15,22.12-43.15h0c11.54,0,21,33.05,22.12,43.15a13,13,0,0,1,0,2.65Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.472px 128.97px;" id="elssqh8n6cmvm" class="animable"></path><path d="M254.53,177.42h0a13.6,13.6,0,0,1-13.6-13.59V107.18a13.6,13.6,0,0,1,13.6-13.59h0a13.6,13.6,0,0,1,13.59,13.59v56.65A13.6,13.6,0,0,1,254.53,177.42Z" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.525px 135.505px;" id="elxdv71vtuhs" class="animable"></path><polygon points="265.39 216.82 243.53 216.82 238.93 201.68 270.04 201.68 265.39 216.82" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.485px 209.25px;" id="elhvs2pdvkg57" class="animable"></polygon><g id="elb8y3p27uxxp"><circle cx="254.48" cy="109.21" r="9.5" style="fill: rgb(38, 50, 56); transform-origin: 254.48px 109.21px; transform: rotate(-21.71deg);" class="animable"></circle></g><g id="el8th69ybbtz"><circle cx="254.48" cy="110.35" r="9.5" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 110.35px; transform: rotate(-80.94deg);" class="animable"></circle></g><path d="M254.48,102.15a8.91,8.91,0,0,1,8.9,8.56c0-.12,0-.24,0-.36a8.92,8.92,0,0,0-17.83,0c0,.12,0,.24,0,.36A8.9,8.9,0,0,1,254.48,102.15Z" style="fill: rgb(38, 50, 56); transform-origin: 254.465px 106.219px;" id="elp1sgbpjnal8" class="animable"></path><g id="elfx1jmq4z3ib"><circle cx="254.48" cy="110.35" r="7.09" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 110.35px; transform: rotate(-80.76deg);" class="animable"></circle></g><path d="M254.48,116.78a7.09,7.09,0,0,0,7.08-6.76c0,.11,0,.22,0,.33a7.09,7.09,0,0,1-14.18,0c0-.11,0-.22,0-.33A7.09,7.09,0,0,0,254.48,116.78Z" style="fill: rgb(38, 50, 56); transform-origin: 254.47px 113.73px;" id="elfwphvsiimf" class="animable"></path><circle cx="254.48" cy="132.67" r="9.5" style="fill: rgb(38, 50, 56); transform-origin: 254.48px 132.67px;" id="el1cmp3b83rj7" class="animable"></circle><circle cx="254.48" cy="133.81" r="9.5" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 133.81px;" id="elqtrvusc7lf" class="animable"></circle><path d="M254.48,125.61a8.91,8.91,0,0,1,8.9,8.56c0-.12,0-.24,0-.36a8.92,8.92,0,0,0-17.83,0c0,.12,0,.24,0,.36A8.9,8.9,0,0,1,254.48,125.61Z" style="fill: rgb(38, 50, 56); transform-origin: 254.465px 129.679px;" id="elbhh6z07eftg" class="animable"></path><circle cx="254.48" cy="133.81" r="7.09" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 133.81px;" id="elja8e25w1j89" class="animable"></circle><path d="M254.48,140.24a7.09,7.09,0,0,0,7.08-6.76c0,.11,0,.22,0,.33a7.09,7.09,0,1,1-14.18,0c0-.11,0-.22,0-.33A7.09,7.09,0,0,0,254.48,140.24Z" style="fill: rgb(38, 50, 56); transform-origin: 254.47px 137.19px;" id="el6fg7b44timm" class="animable"></path><g id="el1xkb7s4j4j9j"><circle cx="254.48" cy="156.13" r="9.5" style="fill: rgb(38, 50, 56); transform-origin: 254.48px 156.13px; transform: rotate(-21.71deg);" class="animable"></circle></g><g id="elltygsw5bvo"><circle cx="254.48" cy="157.27" r="9.5" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 157.27px; transform: rotate(-80.94deg);" class="animable"></circle></g><path d="M254.48,149.07a8.91,8.91,0,0,1,8.9,8.56c0-.12,0-.24,0-.36a8.92,8.92,0,0,0-17.83,0c0,.12,0,.24,0,.36A8.9,8.9,0,0,1,254.48,149.07Z" style="fill: rgb(38, 50, 56); transform-origin: 254.465px 153.139px;" id="el8w3ftpp6szx" class="animable"></path><g id="eld4y1b8urbo"><circle cx="254.48" cy="157.27" r="7.09" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 157.27px; transform: rotate(-80.76deg);" class="animable"></circle></g><path d="M254.48,163.7a7.09,7.09,0,0,0,7.08-6.76c0,.11,0,.22,0,.33a7.09,7.09,0,0,1-14.18,0c0-.11,0-.22,0-.33A7.09,7.09,0,0,0,254.48,163.7Z" style="fill: rgb(38, 50, 56); transform-origin: 254.47px 160.65px;" id="el8yjpjt1s0a3" class="animable"></path><path d="M238.74,75.34h31.48c-4-10.57-9.55-20.39-15.74-20.39h0C248.3,55,242.72,64.77,238.74,75.34Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 254.48px 65.145px;" id="elsliperyvqf" class="animable"></path><line x1="233.68" y1="169.13" x2="233.71" y2="169.73" style="fill: rgb(252, 252, 252); transform-origin: 233.695px 169.43px;" id="elqv5ye4m40xk" class="animable"></line><line x1="233.69" y1="169.28" x2="233.71" y2="169.73" style="fill: rgb(155, 167, 172); transform-origin: 233.7px 169.505px;" id="elstzvm3oa0p" class="animable"></line><line x1="233.7" y1="169.54" x2="233.71" y2="169.73" style="fill: rgb(105, 123, 130); transform-origin: 233.705px 169.635px;" id="elrgq1zfek5bd" class="animable"></line></g><g id="freepik--Building--inject-16" class="animable" style="transform-origin: 265.44px 326.645px;"><rect x="150.97" y="252.63" width="228.78" height="148.03" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 265.36px 326.645px;" id="elnzb7ut5e0cs" class="animable"></rect><rect x="150.97" y="354.79" width="228.78" height="45.87" style="fill: rgb(107, 107, 107); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 265.36px 377.725px;" id="elrgydpt429wm" class="animable"></rect><rect x="156.52" y="315.38" width="78.6" height="60.22" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 195.82px 345.49px;" id="el86p29w6yc2u" class="animable"></rect><rect x="159.68" y="319" width="72.26" height="52.97" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 195.81px 345.485px;" id="elzqufqbew4vi" class="animable"></rect><line x1="179.66" y1="364.75" x2="176.09" y2="367.91" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 177.875px 366.33px;" id="el2xtog6vasx4" class="animable"></line><line x1="229.29" y1="320.78" x2="184.79" y2="360.2" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 207.04px 340.49px;" id="ela354y956rja" class="animable"></line><line x1="219.15" y1="335.62" x2="193.7" y2="358.17" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 206.425px 346.895px;" id="el7tsldvpbreq" class="animable"></line><rect x="296.11" y="315.38" width="78.6" height="60.22" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 335.41px 345.49px;" id="el4uh73pzcq7j" class="animable"></rect><rect x="299.28" y="319" width="72.26" height="52.97" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 335.41px 345.485px;" id="elrxzt20p6xlg" class="animable"></rect><line x1="319.26" y1="364.75" x2="315.69" y2="367.91" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 317.475px 366.33px;" id="elpn4wn085x9" class="animable"></line><line x1="368.88" y1="320.78" x2="324.39" y2="360.2" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 346.635px 340.49px;" id="ellbm78794izr" class="animable"></line><line x1="358.75" y1="335.62" x2="333.3" y2="358.17" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 346.025px 346.895px;" id="el4j6wqrsf1y" class="animable"></line><polygon points="300.11 391.61 296.11 385.36 302.36 387.61 301.61 381.61 304.86 386.61 305.11 377.61 306.86 383.61 310.86 372.36 311.36 384.11 316.11 381.36 313.36 386.61 316.86 384.36 316.61 388.11 321.11 385.86 318.11 391.36 300.11 391.61" style="fill: rgb(186, 104, 200); transform-origin: 308.61px 381.985px;" id="el6pdrjygtn56" class="animable"></polygon><polygon points="326.36 391.61 322.36 385.36 328.61 387.61 327.86 381.61 331.11 386.61 331.36 377.61 333.11 383.61 337.11 372.36 337.61 384.11 342.36 381.36 339.61 386.61 343.11 384.36 342.86 388.11 347.36 385.86 344.36 391.36 326.36 391.61" style="fill: rgb(186, 104, 200); transform-origin: 334.86px 381.985px;" id="el5f83sqg8zqs" class="animable"></polygon><polygon points="352.36 391.61 348.36 385.36 354.61 387.61 353.86 381.61 357.11 386.61 357.36 377.61 359.11 383.61 363.11 372.36 363.61 384.11 368.36 381.36 365.61 386.61 369.11 384.36 368.86 388.11 373.36 385.86 370.36 391.36 352.36 391.61" style="fill: rgb(186, 104, 200); transform-origin: 360.86px 381.985px;" id="eln0xj07cwuis" class="animable"></polygon><polygon points="158.86 391.61 154.86 385.36 161.11 387.61 160.36 381.61 163.61 386.61 163.86 377.61 165.61 383.61 169.61 372.36 170.11 384.11 174.86 381.36 172.11 386.61 175.61 384.36 175.36 388.11 179.86 385.86 176.86 391.36 158.86 391.61" style="fill: rgb(186, 104, 200); transform-origin: 167.36px 381.985px;" id="eldy1n17zp2he" class="animable"></polygon><polygon points="185.11 391.61 181.11 385.36 187.36 387.61 186.61 381.61 189.86 386.61 190.11 377.61 191.86 383.61 195.86 372.36 196.36 384.11 201.11 381.36 198.36 386.61 201.86 384.36 201.61 388.11 206.11 385.86 203.11 391.36 185.11 391.61" style="fill: rgb(186, 104, 200); transform-origin: 193.61px 381.985px;" id="eloib0dm1t7e" class="animable"></polygon><polygon points="211.11 391.61 207.11 385.36 213.36 387.61 212.61 381.61 215.86 386.61 216.11 377.61 217.86 383.61 221.86 372.36 222.36 384.11 227.11 381.36 224.36 386.61 227.86 384.36 227.61 388.11 232.11 385.86 229.11 391.36 211.11 391.61" style="fill: rgb(186, 104, 200); transform-origin: 219.61px 381.985px;" id="elwb79ptbn9l" class="animable"></polygon><rect x="150.97" y="390.05" width="228.78" height="10.6" style="fill: rgb(107, 107, 107); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 265.36px 395.35px;" id="eljte1c65qyn" class="animable"></rect><rect x="240.25" y="310.24" width="49.6" height="90.41" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 265.05px 355.445px;" id="el4z3y9n70czo" class="animable"></rect><path d="M285.92,361.73H243.39V314.64h42.53Zm0,5H243.39v28.88h42.53Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 264.655px 355.125px;" id="elvn45qllgjn" class="animable"></path><rect x="286.89" y="350.66" width="1.55" height="16.51" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 287.665px 358.915px;" id="elgri8ay52jva" class="animable"></rect><line x1="249.96" y1="352.16" x2="247.5" y2="354.34" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 248.73px 353.25px;" id="el5rdco2ya7df" class="animable"></line><line x1="284.17" y1="321.86" x2="253.49" y2="349.03" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 268.83px 335.445px;" id="el08snj4ab1745" class="animable"></line><line x1="277.18" y1="332.08" x2="259.64" y2="347.63" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 268.41px 339.855px;" id="elv7n6nj1hhx" class="animable"></line><line x1="257.85" y1="390.87" x2="255.96" y2="392.55" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 256.905px 391.71px;" id="elcb7z6fovj3" class="animable"></line><line x1="284.17" y1="367.55" x2="260.57" y2="388.46" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 272.37px 378.005px;" id="elfzip1seedig" class="animable"></line><line x1="278.79" y1="375.42" x2="265.3" y2="387.38" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 272.045px 381.4px;" id="elmfx53stnryi" class="animable"></line><g id="el3whn5f27rna"><rect x="151.46" y="287.48" width="228.29" height="30.72" style="opacity: 0.3; transform-origin: 265.605px 302.84px;" class="animable"></rect></g><rect x="147.72" y="269.74" width="235.44" height="43.82" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 265.44px 291.65px;" id="elipxq437fniq" class="animable"></rect><rect x="160.3" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 165.72px 291.65px;" id="eldoqig2ogqsa" class="animable"></rect><rect x="183.27" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 188.69px 291.65px;" id="elmtrt9gy0m3b" class="animable"></rect><rect x="206.25" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 211.67px 291.65px;" id="elmvuab3w56ph" class="animable"></rect><rect x="228.11" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 233.53px 291.65px;" id="el3lvlv6h5hu1" class="animable"></rect><rect x="249.97" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 255.39px 291.65px;" id="elnujnkpuusfi" class="animable"></rect><rect x="271.82" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 277.24px 291.65px;" id="elbesd8i9mx7" class="animable"></rect><rect x="293.68" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 299.1px 291.65px;" id="elrc40jsgrl6f" class="animable"></rect><rect x="315.54" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 320.96px 291.65px;" id="el6w9508fkz6u" class="animable"></rect><rect x="337.4" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 342.82px 291.65px;" id="elabo7fgjnrcf" class="animable"></rect><rect x="359.25" y="269.74" width="10.84" height="43.82" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 364.67px 291.65px;" id="el9c3rqlytrzo" class="animable"></rect></g><g id="freepik--Tree--inject-16" class="animable" style="transform-origin: 401.882px 288.352px;"><path d="M409.63,295.74s-4.41-8-16-12.82S358,271.3,358.36,265.3s19.22,1.2,19.22,1.2-31.24-15.22-35.25-24.84,28.85.8,28.85.8-14.83-28-10.82-34.44,18.43,5.6,18.43,5.6-5.21-34.45,20-38.05,25.64,22.83,25.64,22.83,8.81-15.62,18.83-12-.81,23.63-.81,23.63,14.83-11.22,18.83,5.61-17.62,35.65-17.62,35.65,14.82-4.81,15.62,3.6-9.62,14-9.62,14,8.82,1.6,5.61,6S412.43,289.73,409.63,295.74Z" style="fill: rgb(186, 104, 200); transform-origin: 401.882px 235.487px;" id="elp3hbdztggrh" class="animable"></path><path d="M433.45,254.91s-14.8,3.51-22.42,15.53l-.28-16.58c1.17-2,9.47-15.18,22.7-19.22,0,0-15.18,4.13-22.75,16.4l-.7-41-.21,49.89C402.34,248.36,388.55,245,388.55,245c12.4,3.93,19.84,15.87,21.23,18.26l-.07,16.39c-7.48-11.42-21.16-14.78-21.16-14.78,12.17,3.86,19.56,15.42,21.14,18.11l-.48,114.26h4l-1.76-103.74c1.38-2.31,9.11-14.35,22-18.3,0,0-14.37,3.41-22.08,15l-.28-16.52C412.56,271.23,420.57,258.84,433.45,254.91Z" style="fill: rgb(38, 50, 56); transform-origin: 411px 303.64px;" id="elxomtrl77fdr" class="animable"></path><rect x="394.43" y="394.22" width="31.57" height="7.25" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 410.215px 397.845px;" id="el3cjxvi1u7lh" class="animable"></rect></g><g id="freepik--Floor--inject-16" class="animable" style="transform-origin: 253.17px 403.61px;"><rect x="101.48" y="400.57" width="331.86" height="6.08" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 267.41px 403.61px;" id="ele1ch0102226" class="animable"></rect><line x1="24.34" y1="406.65" x2="482" y2="406.65" style="fill: none; stroke: rgb(38, 50, 56); stroke-miterlimit: 10; stroke-width: 0.950778px; transform-origin: 253.17px 406.65px;" id="elcy52lp32cr" class="animable"></line></g><g id="freepik--Character--inject-16" class="animable" style="transform-origin: 115.18px 330.199px;"><ellipse cx="115.18" cy="455.37" rx="64.18" ry="14.06" style="fill: rgb(186, 104, 200); transform-origin: 115.18px 455.37px;" id="el0a4k910n6nf9" class="animable"></ellipse><g id="elgb0raudvell"><ellipse cx="115.18" cy="455.37" rx="64.18" ry="14.06" style="fill: rgb(255, 255, 255); opacity: 0.5; transform-origin: 115.18px 455.37px;" class="animable"></ellipse></g><path d="M104.17,311.63s-6.81,20.93-8.33,31.52-4.54,40.1-4.54,40.1a49.18,49.18,0,0,0-10.21,23.07C79.2,419.82,79.83,438,79.83,438s-1.51,2.27-1.51,3.41.25,3.66.25,3.66A94.91,94.91,0,0,0,70.62,452c-2.77,2.9-2.27,7.95-2.27,9.33s1.64,3.41,2.53,3.41,6.93-1.14,10-3.66,3.15-7.69,3.91-10S90.67,442,90.67,442a3.37,3.37,0,0,0,3-3c.25-2.77.88-7.69,1.13-8.7s10.85-18.16,13.24-28.5,9.84-25.84,12.49-32.65,8.32-20.68,8.32-20.68l1.14-.38s-2.15,30-2.78,39.47-.76,11.85-.76,24.33,4.92,33,4.92,33v6.43c0,.38,1,.76,2.9.88s7.46.15,8.85,1,7.06,1.51,12.86,1.26,8.83-1.26,8.2-2.4-10.59-5-12.23-6.05-5.45-3.56-5.45-3.56.26-3.27-.63-4.79-1.26-1.38-1.26-3,3.66-23.08,4.67-33.29,6.43-24.72,6.81-35.56-.63-19.17-1.52-31.53a103.74,103.74,0,0,0-3.9-21.81Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 116.288px 388.185px;" id="elspzmcymkw1d" class="animable"></path><polygon points="145.15 312.4 127.62 312.07 127.62 316.29 145.15 316.29 145.15 312.4" style="fill: rgb(89, 89, 89); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 136.385px 314.18px;" id="elp23l19z1h3" class="animable"></polygon><path d="M147.29,316.29h4.44c-.59-2.4-1-3.78-1-3.78l-3.41-.07Z" style="fill: rgb(89, 89, 89); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 149.51px 314.365px;" id="el2jv9kkea2of" class="animable"></path><polygon points="112.61 316.29 123.33 316.29 123.33 311.99 112.61 311.79 112.61 316.29" style="fill: rgb(89, 89, 89); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 117.97px 314.04px;" id="elujrk1ws89ep" class="animable"></polygon><path d="M110.34,311.74l-6.17-.11s-.6,1.82-1.47,4.66h7.64Z" style="fill: rgb(89, 89, 89); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 106.52px 313.96px;" id="el087mk7zonumk" class="animable"></path><rect x="133.55" y="312.26" width="7.06" height="3.91" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 137.08px 314.215px;" id="elmcz9imm82jf" class="animable"></rect><path d="M167.34,328.78s2.27,9.7,1.64,15.63c-.24,2.26-3.28,8.83-3.91,9.21a3.1,3.1,0,0,1-1.89,0,29,29,0,0,0,1.13-3.79c.13-1,.26-3.78.26-3.78a36.55,36.55,0,0,1-2.53,4.67,12,12,0,0,1-2.9,2.14,1.5,1.5,0,0,1-1.13-.63c-.25-.38,1-1.26,1.64-2.65s1.64-4,1.64-4a27.47,27.47,0,0,1-3,3.27,11,11,0,0,1-3.28,1.64c-.38,0-1.13-1.38-.75-1.51s3.53-1.89,3.9-2.52a14.34,14.34,0,0,1,1.14-1.51,38.08,38.08,0,0,1-4.54,1.51c-.63,0-.88-1-.88-1l4.16-2.14,1-2.53a11.57,11.57,0,0,1,0-5.92c.88-2.9,1.26-4.92,1.26-4.92Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 161.485px 341.274px;" id="el2nbz9d86znu" class="animable"></path><path d="M162.93,332.94s.12,4.16.88,5,2.14,1.89,2.14,2.77-1.38,5.93-1,6.31a1,1,0,0,0,1.77-.38,47.86,47.86,0,0,0,1.64-8.45" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 165.645px 340.177px;" id="el6qzcp7qd2af" class="animable"></path><path d="M156.62,259.67s3.41,20.56,3.78,26.74.38,10.84.38,10.84l6.56,31.53-7.06,1.13s-8.32-17-10-19-2.53-5.8-1.89-11.22.12-35.06,1.38-38.59A4.05,4.05,0,0,1,156.62,259.67Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 157.758px 294.121px;" id="eluie14koyms" class="animable"></path><line x1="159.4" y1="327.89" x2="166.46" y2="326" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 162.93px 326.945px;" id="elgup4q85fdyi" class="animable"></line><path d="M116.27,239s-27.62,11.46-28.88,12-2.14,0-2.77,2.52S73.52,301,74,304.56,82.22,339,82.22,339l8.71-1.51,1-37.2,4.67-11.86s6.18,15.51,6.05,16.78-1.64,4.16-.75,5.8a6.35,6.35,0,0,0,1.89,2.27h48s3-1,3.15-2.4a40.19,40.19,0,0,0-.75-6.05s4.28-16.9,4.28-28.63-1.38-21.43-3.28-24.21-8.7-5.55-14.87-7.69-11.35-4.16-16.9-4.66S116.27,239,116.27,239Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 116.227px 289px;" id="elda2jsqidxbe" class="animable"></path><path d="M137.2,248.45s4.28,19.22,4.28,30,.29,23.68.87,25.73,1.47,5.55-.58,7.89" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 140.125px 280.26px;" id="elzu0mf8h9ugl" class="animable"></path><line x1="96.6" y1="288.42" x2="104.29" y2="269.76" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 100.445px 279.09px;" id="elr1lh7jkdfl" class="animable"></line><line x1="100.51" y1="279.85" x2="107.7" y2="270.77" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 104.105px 275.31px;" id="elpq7fd9sa0h" class="animable"></line><line x1="81.34" y1="335.08" x2="90.93" y2="334.07" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 86.135px 334.575px;" id="elzgg6xiqy5o" class="animable"></line><polygon points="142.5 242.53 145.4 248.7 138.84 245.05 142.5 242.53" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 142.12px 245.615px;" id="eln3rn7dwsef" class="animable"></polygon><polygon points="137.2 248.45 142.5 242.53 137.33 240 137.2 248.45" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 139.85px 244.225px;" id="elep5xj790pb8" class="animable"></polygon><path d="M138.09,200.54s3.4,3.78,3.4,4.91a23.09,23.09,0,0,1-.25,2.78s5.67,2.77,5,3.91-1.51,3.27-1.51,3.27,4.66,10.22,4,11.23-7.06,7.18-8.19,10.21-3.41,11.6-3.41,11.6l-21.69-12s2.65-4.28,2.78-7.18.5-5.17,0-6.94-3.15-6.31-3.41-10.72,4.54-9.58,10.85-12.61S138.09,200.54,138.09,200.54Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 131.797px 223.206px;" id="ela6v3k4hq5xi" class="animable"></path><path d="M135.06,218.31a5.17,5.17,0,0,0,6.05,1.27" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 138.085px 219.185px;" id="elasdu73ghj9i" class="animable"></path><path d="M137.87,210.29c.34.73.22,1.5-.25,1.71s-1.13-.19-1.47-.92-.21-1.5.26-1.71S137.54,209.56,137.87,210.29Z" style="fill: rgb(38, 50, 56); transform-origin: 137.012px 210.685px;" id="elaekh6elf7xv" class="animable"></path><path d="M132.66,209.74a4.42,4.42,0,0,1,4.67-2.77" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.90156px; transform-origin: 134.995px 208.337px;" id="elxhx08ev6kg" class="animable"></path><path d="M123.08,221.47s3.28,0,3.28-1.77-2.52-5.29-1.89-7.06,3.65-3,2.52-4.92-3.41-1.89-2.14-3.15,10.59.38,13.74-3S143,193,140.73,192s-5.17,1.51-5.17,1.51-1.51-3.53-6.05-2.27-10.84,6.18-14.37,11.1-4.8,6.68-3.41,10,5.93,9.46,7.69,10.47S123.08,221.47,123.08,221.47Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 126.532px 207.015px;" id="elo5b8wpbenk" class="animable"></path><path d="M125.1,223.11s-3.53-5.05-6.56-4.42-2.27,5.05-.25,7.06,5.55.63,6.3.13c0,0,2.78,6.68,5.55,6.94s10.59-3.16,10.59-3.16" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 128.623px 225.735px;" id="elb058nzou1sh" class="animable"></path><polygon points="133.29 241.64 137.2 248.45 129.64 244.67 133.29 241.64" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 133.42px 245.045px;" id="elmln86kg3kf" class="animable"></polygon><polygon points="115.89 233.32 133.29 241.64 129.26 251.48 113.12 239.37 115.89 233.32" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 123.205px 242.4px;" id="elo1vnc1tenyh" class="animable"></polygon><path d="M139.85,317.8s1.64,10.6-.13,16.4" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 140.131px 326px;" id="el0ofm4l95yq4l" class="animable"></path><path d="M134.18,320.2s1.64,10.47,1,17.78" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 134.754px 329.09px;" id="eldigctr3ms18" class="animable"></path><path d="M140,336c-.38.38-3.28,1.77-4,3.53a54.34,54.34,0,0,1-2.65,4.92" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 136.675px 340.225px;" id="elzfdb62d6q7a" class="animable"></path><line x1="120.05" y1="317.17" x2="116.65" y2="327.51" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 118.35px 322.34px;" id="el5ut9w2p6efa" class="animable"></line><line x1="121.44" y1="317.3" x2="120.68" y2="333.57" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 121.06px 325.435px;" id="elijvix00azb" class="animable"></line><line x1="148.8" y1="317.17" x2="150.95" y2="326.51" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 149.875px 321.84px;" id="elh9mnez1avdd" class="animable"></line><path d="M148.3,317.43s-.88,8.82-.88,9.2" style="fill: none; stroke: rgb(92, 101, 106); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 147.86px 322.03px;" id="el6usbhqc474d" class="animable"></path><path d="M94.78,353.35H76.69a1.4,1.4,0,0,0-1.4,1.4v7.38a1.4,1.4,0,0,0,1.4,1.4h2.12a1.4,1.4,0,0,1-1.4-1.4v-4.82a1.4,1.4,0,0,1,1.4-1.4H92.66a1.4,1.4,0,0,1,1.4,1.4v4.82a1.4,1.4,0,0,1-1.4,1.4h2.12a1.4,1.4,0,0,0,1.4-1.4v-7.38A1.4,1.4,0,0,0,94.78,353.35Z" style="fill: rgb(38, 50, 56); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 85.735px 358.44px;" id="eldd5dq5qqw5o" class="animable"></path><path d="M82.22,339s-2.65,10.34-2.15,12.1,1.9,2.78,2,3.16a1.32,1.32,0,0,1-.51,1.51l-.63.38s-.5,0-.12.75a2.06,2.06,0,0,0,.8,1c.83.42,1.34-.43,2.09-.54s1.22.71,1.93.66,1.09-.76,1.78-.7,1.14,1.31,2.14.87c.43-.2.74-.63,1.17-.85.64-.33,1.55-.24,2.12-.73a2.93,2.93,0,0,0,.8-1.76c.23-1.29-2.74-17.39-2.74-17.39Z" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 86.83px 347.872px;" id="eltbzjbd4yplr" class="animable"></path><rect x="53.72" y="360.32" width="61.62" height="42.06" rx="3.99" style="fill: rgb(186, 104, 200); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 84.53px 381.35px;" id="eltijef0kqmul" class="animable"></rect><path d="M53.86,364.2,83,384.78a4.64,4.64,0,0,0,5.41,0l26.77-19.48" style="fill: none; stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 84.52px 374.925px;" id="elffsypxgwlm" class="animable"></path><rect x="83.19" y="382.01" width="4.96" height="6.97" style="fill: rgb(255, 255, 255); stroke: rgb(38, 50, 56); stroke-linecap: round; stroke-linejoin: round; stroke-width: 0.950778px; transform-origin: 85.67px 385.495px;" id="elrnt83mf0oua" class="animable"></rect></g><defs>     <filter id="active" height="200%">         <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius="2"></feMorphology>                <feFlood flood-color="#32DFEC" flood-opacity="1" result="PINK"></feFlood>        <feComposite in="PINK" in2="DILATED" operator="in" result="OUTLINE"></feComposite>        <feMerge>            <feMergeNode in="OUTLINE"></feMergeNode>            <feMergeNode in="SourceGraphic"></feMergeNode>        </feMerge>    </filter>    <filter id="hover" height="200%">        <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius="2"></feMorphology>                <feFlood flood-color="#ff0000" flood-opacity="0.5" result="PINK"></feFlood>        <feComposite in="PINK" in2="DILATED" operator="in" result="OUTLINE"></feComposite>        <feMerge>            <feMergeNode in="OUTLINE"></feMergeNode>            <feMergeNode in="SourceGraphic"></feMergeNode>        </feMerge>            <feColorMatrix type="matrix" values="0   0   0   0   0                0   1   0   0   0                0   0   0   0   0                0   0   0   1   0 "></feColorMatrix>    </filter></defs></svg>