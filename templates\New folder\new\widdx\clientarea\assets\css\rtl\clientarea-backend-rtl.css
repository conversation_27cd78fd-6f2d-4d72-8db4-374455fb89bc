/*
 * RTL Styles for WHMCS WIDDX Theme - Client Area Backend Specific
 */

/* Client Area Backend Specific RTL Fixes */

/* Fix for client area backend layout */
html[dir="rtl"] .layout-wrapper.layout-content-navbar .layout-container {
  flex-direction: row-reverse;
}

/* Fix for client area backend menu */
html[dir="rtl"] .layout-menu {
  right: 0;
  left: auto;
  border-right: 0;
  border-left: 1px solid var(--border-color);
}

html[dir="rtl"] .layout-page {
  padding-right: 16.25rem;
  padding-left: 0;
}

/* Fix for client area backend menu items */
html[dir="rtl"] .menu-inner .menu-item .menu-link {
  text-align: right;
}

html[dir="rtl"] .menu-inner .menu-item .menu-link .menu-icon {
  margin-right: 0;
  margin-left: 1rem;
}

html[dir="rtl"] .menu-inner .menu-item .menu-toggle::after {
  margin-right: auto;
  margin-left: 0;
  transform: rotate(180deg);
}

html[dir="rtl"] .menu-inner .menu-item .menu-toggle[aria-expanded="true"]::after {
  transform: rotate(270deg);
}

html[dir="rtl"] .menu-inner .menu-sub {
  padding-right: 1.5rem;
  padding-left: 0;
}

/* Fix for client area backend navbar */
html[dir="rtl"] .layout-navbar .container-xxl {
  flex-direction: row-reverse;
}

html[dir="rtl"] .layout-navbar .navbar-nav-right {
  margin-left: 0;
  margin-right: auto;
}

html[dir="rtl"] .layout-navbar .navbar-brand {
  margin-right: 0;
  margin-left: 1rem;
}

/* Fix for client area backend search */
html[dir="rtl"] .navbar-search-wrapper {
  margin-left: 0;
  margin-right: auto;
}

html[dir="rtl"] .navbar-search .form-control {
  padding-right: 2.5rem;
  padding-left: 0.75rem;
  text-align: right;
}

html[dir="rtl"] .navbar-search .search-icon {
  right: 0.75rem;
  left: auto;
}

/* Fix for client area backend cards */
html[dir="rtl"] .card {
  text-align: right;
}

html[dir="rtl"] .card-header {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  direction: ltr;
}

html[dir="rtl"] .card-title {
  margin-right: 0;
  margin-left: auto;
}

html[dir="rtl"] .card-header .card-minimise {
  margin-left: 0;
  margin-right: auto;
}

/* Fix for client area backend tables */
html[dir="rtl"] .table th,
html[dir="rtl"] .table td {
  text-align: right;
}

html[dir="rtl"] .dataTables_wrapper .dataTables_filter {
  text-align: left;
}

html[dir="rtl"] .dataTables_wrapper .dataTables_filter input {
  margin-left: 0;
  margin-right: 0.5em;
}

html[dir="rtl"] .dataTables_wrapper .dataTables_length {
  text-align: right;
}

html[dir="rtl"] .dataTables_wrapper .dataTables_info {
  text-align: right;
}

html[dir="rtl"] .dataTables_wrapper .dataTables_paginate {
  text-align: left;
}

/* Fix for client area backend forms */
html[dir="rtl"] .form-label {
  text-align: right;
}

html[dir="rtl"] .form-check {
  padding-left: 0;
  padding-right: 1.5rem;
}

html[dir="rtl"] .form-check .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.5rem;
}

html[dir="rtl"] .form-control {
  text-align: right;
  direction: rtl;
}

html[dir="rtl"] .form-select {
  text-align: right;
  padding-right: 0.75rem;
  padding-left: 2.25rem;
  background-position: left 0.75rem center;
}

/* Fix for client area backend input groups */
html[dir="rtl"] .input-group {
  direction: rtl;
}

html[dir="rtl"] .input-group > .form-control:not(:last-child),
html[dir="rtl"] .input-group > .custom-select:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

html[dir="rtl"] .input-group > .form-control:not(:first-child),
html[dir="rtl"] .input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Fix for client area backend buttons */
html[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
html[dir="rtl"] .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

html[dir="rtl"] .btn-group > .btn:not(:first-child),
html[dir="rtl"] .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Fix for client area backend dropdowns */
html[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}

html[dir="rtl"] .dropdown-item {
  text-align: right;
}

/* Fix for client area backend alerts */
html[dir="rtl"] .alert-dismissible {
  padding-right: 1.25rem;
  padding-left: 4rem;
}

html[dir="rtl"] .alert-dismissible .close {
  left: 0;
  right: auto;
}

/* Fix for client area backend modals */
html[dir="rtl"] .modal-header .close {
  margin: -1rem auto -1rem -1rem;
}

html[dir="rtl"] .modal-footer {
  justify-content: flex-start;
}

/* Fix for client area backend icons */
html[dir="rtl"] .bx-chevron-right:before {
  content: "\ea64";
}

html[dir="rtl"] .bx-chevron-left:before {
  content: "\ea6e";
}

/* Fix for client area backend sidebar */
html[dir="rtl"] .sidebar {
  text-align: right;
}

html[dir="rtl"] .sidebar .nav-link {
  text-align: right;
}

html[dir="rtl"] .sidebar .nav-link i {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Fix for client area backend pagination */
html[dir="rtl"] .pagination {
  padding-right: 0;
}

html[dir="rtl"] .pagination .page-link {
  border-radius: 0 !important;
}

/* Fix for client area backend breadcrumbs */
html[dir="rtl"] .breadcrumb {
  padding-right: 0;
  padding-left: 1rem;
}

html[dir="rtl"] .breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
  padding-left: 0;
}

html[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  padding-right: 0;
  padding-left: 0.5rem;
}

/* Fix for client area backend widgets */
html[dir="rtl"] .widget-card {
  text-align: right;
}

html[dir="rtl"] .widget-card .widget-icon {
  right: auto;
  left: 1rem;
}

/* Fix for client area backend tiles */
html[dir="rtl"] .tiles .tile .stat {
  margin-top: 20px;
  font-size: 40px;
  line-height: 1;
  direction: ltr;
}
