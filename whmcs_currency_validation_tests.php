<?php
/**
 * WHMCS Lahza Payment Gateway - Currency & Amount Validation Testing
 * 
 * Comprehensive testing of currency conversion and amount validation
 * including precision handling, minimum amounts, and edge cases
 */

// Mock WHMCS environment
define('WHMCS', true);

// Include gateway files
require_once __DIR__ . '/modules/gateways/lahza.php';

class WHMCSCurrencyValidationTests {
    
    private $testResults = [];
    private $passedTests = 0;
    private $failedTests = 0;
    
    // Currency test matrix
    private $currencyTests = [
        'ILS' => [
            'name' => 'Israeli Shekel',
            'symbol' => '₪',
            'multiplier' => 100,
            'decimal_places' => 2,
            'test_amounts' => [1.00, 10.50, 100.00, 999.99, 0.01],
            'min_amount' => 1.00
        ],
        'USD' => [
            'name' => 'US Dollar',
            'symbol' => '$',
            'multiplier' => 100,
            'decimal_places' => 2,
            'test_amounts' => [1.00, 25.50, 100.00, 500.00, 0.01],
            'min_amount' => 1.00
        ],
        'JOD' => [
            'name' => 'Jordanian Dinar',
            'symbol' => 'د.ا',
            'multiplier' => 1000,
            'decimal_places' => 3,
            'test_amounts' => [1.000, 10.500, 50.000, 200.000, 0.001],
            'min_amount' => 1.000
        ]
    ];
    
    public function __construct() {
        echo "💱 WHMCS Lahza Payment Gateway - Currency & Amount Validation Testing\n";
        echo str_repeat("=", 75) . "\n";
        echo "Testing Environment: WHMCS with WIDDX Theme\n";
        echo "Supported Currencies: ILS, USD, JOD\n";
        echo "Gateway Version: 1.1.0 (Enhanced)\n";
        echo str_repeat("=", 75) . "\n\n";
    }
    
    /**
     * Run all currency validation tests
     */
    public function runAllCurrencyTests() {
        echo "💱 Testing Currency & Amount Validation\n";
        echo str_repeat("-", 40) . "\n";
        
        // Test 1: Currency Conversion Accuracy
        $this->testCurrencyConversionAccuracy();
        
        // Test 2: Amount Precision Handling
        $this->testAmountPrecisionHandling();
        
        // Test 3: Minimum Amount Validation
        $this->testMinimumAmountValidation();
        
        // Test 4: Maximum Amount Handling
        $this->testMaximumAmountHandling();
        
        // Test 5: Edge Cases
        $this->testCurrencyEdgeCases();
        
        // Test 6: Invalid Currency Handling
        $this->testInvalidCurrencyHandling();
        
        // Test 7: WHMCS Integration
        $this->testWHMCSCurrencyIntegration();
        
        // Generate report
        $this->generateCurrencyReport();
    }
    
    /**
     * Test currency conversion accuracy
     */
    private function testCurrencyConversionAccuracy() {
        echo "\n🎯 Testing Currency Conversion Accuracy\n";
        echo str_repeat("-", 40) . "\n";
        
        foreach ($this->currencyTests as $currency => $config) {
            foreach ($config['test_amounts'] as $amount) {
                $this->testCurrencyConversion($currency, $amount, $config['multiplier']);
            }
        }
    }
    
    /**
     * Test amount precision handling
     */
    private function testAmountPrecisionHandling() {
        echo "\n🔢 Testing Amount Precision Handling\n";
        echo str_repeat("-", 35) . "\n";
        
        // Test precision for each currency
        $precisionTests = [
            'ILS' => [1.234, 10.567, 100.999], // Should round to 2 decimals
            'USD' => [1.234, 25.567, 500.999], // Should round to 2 decimals
            'JOD' => [1.2345, 10.5678, 50.9999] // Should handle 3 decimals
        ];
        
        foreach ($precisionTests as $currency => $amounts) {
            foreach ($amounts as $amount) {
                $this->testAmountPrecision($currency, $amount);
            }
        }
    }
    
    /**
     * Test minimum amount validation
     */
    private function testMinimumAmountValidation() {
        echo "\n📏 Testing Minimum Amount Validation\n";
        echo str_repeat("-", 35) . "\n";
        
        foreach ($this->currencyTests as $currency => $config) {
            // Test below minimum
            $this->testMinimumAmount($currency, $config['min_amount'] - 0.01, false);
            
            // Test at minimum
            $this->testMinimumAmount($currency, $config['min_amount'], true);
            
            // Test above minimum
            $this->testMinimumAmount($currency, $config['min_amount'] + 1.00, true);
        }
    }
    
    /**
     * Test maximum amount handling
     */
    private function testMaximumAmountHandling() {
        echo "\n📊 Testing Maximum Amount Handling\n";
        echo str_repeat("-", 35) . "\n";
        
        // Test large amounts
        $largeAmounts = [
            'ILS' => 999999.99,
            'USD' => 999999.99,
            'JOD' => 999999.999
        ];
        
        foreach ($largeAmounts as $currency => $amount) {
            $this->testLargeAmount($currency, $amount);
        }
    }
    
    /**
     * Test currency edge cases
     */
    private function testCurrencyEdgeCases() {
        echo "\n⚠️ Testing Currency Edge Cases\n";
        echo str_repeat("-", 30) . "\n";
        
        // Test zero amounts
        foreach (array_keys($this->currencyTests) as $currency) {
            $this->testEdgeCase($currency, 0, 'Zero amount');
        }
        
        // Test negative amounts
        foreach (array_keys($this->currencyTests) as $currency) {
            $this->testEdgeCase($currency, -10.00, 'Negative amount');
        }
        
        // Test very small amounts
        $this->testEdgeCase('ILS', 0.001, 'Very small amount');
        $this->testEdgeCase('JOD', 0.0001, 'Very small amount');
    }
    
    /**
     * Test invalid currency handling
     */
    private function testInvalidCurrencyHandling() {
        echo "\n🚫 Testing Invalid Currency Handling\n";
        echo str_repeat("-", 35) . "\n";
        
        $invalidCurrencies = ['EUR', 'GBP', 'JPY', '', 'INVALID', '123'];
        
        foreach ($invalidCurrencies as $currency) {
            $this->testInvalidCurrency($currency);
        }
    }
    
    /**
     * Test WHMCS currency integration
     */
    private function testWHMCSCurrencyIntegration() {
        echo "\n🔧 Testing WHMCS Currency Integration\n";
        echo str_repeat("-", 37) . "\n";
        
        foreach ($this->currencyTests as $currency => $config) {
            $this->testWHMCSIntegration($currency, 100.00);
        }
    }
    
    /**
     * Test individual currency conversion
     */
    private function testCurrencyConversion($currency, $amount, $expectedMultiplier) {
        $testName = "Currency Conversion - {$currency} {$amount}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit($amount, $currency);
            $expected = (int)($amount * $expectedMultiplier);
            
            $success = ($result === $expected);
            
            if (!$success) {
                $error = "Expected {$expected}, got {$result}";
                $this->recordTestResult($testName, false, microtime(true) - $testStart, $error);
            } else {
                $this->recordTestResult($testName, true, microtime(true) - $testStart);
            }
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test amount precision
     */
    private function testAmountPrecision($currency, $amount) {
        $testName = "Precision - {$currency} {$amount}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit($amount, $currency);
            
            // Check if conversion handles precision correctly
            $success = is_int($result) && $result > 0;
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test minimum amount
     */
    private function testMinimumAmount($currency, $amount, $shouldPass) {
        $testName = "Min Amount - {$currency} {$amount}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit($amount, $currency);
            
            // If we expect it to pass, result should be positive integer
            // If we expect it to fail, it should throw exception
            $success = $shouldPass ? (is_int($result) && $result > 0) : false;
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            // Exception is expected for amounts below minimum
            $success = !$shouldPass;
            $this->recordTestResult($testName, $success, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test large amount
     */
    private function testLargeAmount($currency, $amount) {
        $testName = "Large Amount - {$currency} {$amount}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit($amount, $currency);
            
            // Should handle large amounts without overflow
            $success = is_int($result) && $result > 0;
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test edge case
     */
    private function testEdgeCase($currency, $amount, $description) {
        $testName = "Edge Case - {$currency} {$description}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit($amount, $currency);
            
            // Edge cases should generally fail (except for valid small amounts)
            $success = false;
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            // Exception is expected for edge cases
            $this->recordTestResult($testName, true, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test invalid currency
     */
    private function testInvalidCurrency($currency) {
        $testName = "Invalid Currency - {$currency}";
        $testStart = microtime(true);
        
        try {
            $result = lahza_convertToSmallestUnit(100.00, $currency);
            
            // Should not succeed for invalid currency
            $this->recordTestResult($testName, false, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            // Exception is expected for invalid currency
            $this->recordTestResult($testName, true, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Test WHMCS integration
     */
    private function testWHMCSIntegration($currency, $amount) {
        $testName = "WHMCS Integration - {$currency}";
        $testStart = microtime(true);
        
        try {
            // Prepare WHMCS parameters
            $params = [
                'invoiceid' => rand(1000, 9999),
                'amount' => $amount,
                'currency' => $currency,
                'clientdetails' => [
                    'userid' => rand(100, 999),
                    'email' => '<EMAIL>',
                    'firstname' => 'Test',
                    'lastname' => 'User'
                ],
                'publicKey' => 'pk_test_0e9Ce7p2rYbMd5Gjwi1JSSfnUUmBs98eQ',
                'secretKey' => 'sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im',
                'testMode' => 'on',
                'systemurl' => 'http://localhost/whmcs/',
                'returnurl' => 'http://localhost/whmcs/viewinvoice.php'
            ];
            
            // Test payment link generation
            $result = lahza_link($params);
            
            // Should generate payment form or handle gracefully
            $success = is_string($result) && !empty($result);
            
            $this->recordTestResult($testName, $success, microtime(true) - $testStart);
            
        } catch (Exception $e) {
            $this->recordTestResult($testName, false, microtime(true) - $testStart, $e->getMessage());
        }
    }
    
    /**
     * Record test result
     */
    private function recordTestResult($testName, $success, $duration, $note = null) {
        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'duration' => round($duration * 1000, 2),
            'note' => $note
        ];
        
        if ($success) {
            $this->passedTests++;
            echo "✅ {$testName} - PASSED";
            if ($note && strpos($note, 'Expected') === false) echo " ({$note})";
            echo "\n";
        } else {
            $this->failedTests++;
            echo "❌ {$testName} - FAILED";
            if ($note) echo " - {$note}";
            echo "\n";
        }
    }
    
    /**
     * Generate currency test report
     */
    private function generateCurrencyReport() {
        $totalTests = $this->passedTests + $this->failedTests;
        
        echo "\n" . str_repeat("=", 75) . "\n";
        echo "📊 CURRENCY & AMOUNT VALIDATION TEST RESULTS\n";
        echo str_repeat("=", 75) . "\n";
        echo "Total Tests: {$totalTests}\n";
        echo "✅ Passed: {$this->passedTests}\n";
        echo "❌ Failed: {$this->failedTests}\n";
        echo "Success Rate: " . round(($this->passedTests / $totalTests) * 100, 2) . "%\n";
        
        echo "\n💱 Currency Support Status:\n";
        foreach ($this->currencyTests as $currency => $config) {
            $currencyTests = array_filter($this->testResults, function($test) use ($currency) {
                return strpos($test['name'], $currency) !== false;
            });
            
            $currencyPassed = count(array_filter($currencyTests, function($test) {
                return $test['success'];
            }));
            
            $currencyTotal = count($currencyTests);
            $currencyRate = $currencyTotal > 0 ? round(($currencyPassed / $currencyTotal) * 100, 1) : 0;
            
            echo "- {$currency} ({$config['name']}): {$currencyRate}% ({$currencyPassed}/{$currencyTotal})\n";
        }
        
        echo "\n🎯 Key Findings:\n";
        echo "- Currency Conversion: " . ($this->passedTests > 0 ? "✅ Working" : "❌ Issues Found") . "\n";
        echo "- Amount Validation: " . ($this->passedTests > 0 ? "✅ Working" : "❌ Issues Found") . "\n";
        echo "- Precision Handling: " . ($this->passedTests > 0 ? "✅ Working" : "❌ Issues Found") . "\n";
        echo "- WHMCS Integration: " . ($this->passedTests > 0 ? "✅ Working" : "❌ Issues Found") . "\n";
        
        if ($this->failedTests > 0) {
            echo "\n❌ FAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "- {$result['name']}";
                    if ($result['note']) {
                        echo " ({$result['note']})";
                    }
                    echo "\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 75) . "\n";
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $testSuite = new WHMCSCurrencyValidationTests();
    $testSuite->runAllCurrencyTests();
}
