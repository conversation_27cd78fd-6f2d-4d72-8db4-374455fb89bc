<!-- Mobile Bottom Navigation -->
<nav class="mobile-bottom-nav d-md-none">
    <div class="container">
        <div class="row justify-content-around">
            <a href="{$WEB_ROOT}/clientarea.php" class="col mobile-nav-item text-center">
                <i class="fas fa-home d-block mb-1"></i>
                <small>{lang key='home'}</small>
            </a>
            <a href="#" class="col mobile-nav-item text-center" data-bs-toggle="modal" data-bs-target="#quickActionsModal">
                <i class="fas fa-layer-group d-block mb-1"></i>
                <small>{lang key='actions'}</small>
            </a>
            <a href="{$WEB_ROOT}/cart.php?a=view" class="col mobile-nav-item text-center">
                <i class="far fa-shopping-cart d-block mb-1"></i>
                <small>{lang key='cart'}</small>
                <span id="cartItemCount" class="badge bg-primary rounded-pill position-absolute top-0 start-80 translate-middle">
                    {$cartitemcount}
                </span>
            </a>
            <a href="#" class="col mobile-nav-item text-center" id="notifDropdown" data-bs-toggle="modal" data-bs-target="#notifModal">
                <i class="fas fa-bell d-block mb-1"></i>
                <small>{lang key='notifications'}</small>
                <span class="badge bg-danger rounded-pill position-absolute top-0 start-80 translate-middle">
                    {$clientAlerts|count|default:0}
                </span>
            </a>
        </div>
    </div>
</nav>

<!-- Notification Modal -->
<div class="modal fade" id="notifModal" tabindex="-1" aria-labelledby="notifModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 bg-light">
                <h5 class="modal-title" id="notifModalLabel">
                    <i class="fas fa-bell me-2 text-primary"></i>
                    {lang key='notifications'} <span class="badge bg-primary rounded-pill ms-2">{$clientAlerts|count|default:0}</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <ul class="list-group list-group-flush">
                    {foreach $clientAlerts as $alert}
                        <li class="list-group-item border-0 py-3">
                            <a href="{$alert->getLink()}" class="text-decoration-none text-dark">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="fas fa-{if $alert->getSeverity() == 'danger'}exclamation-circle text-danger
                                                      {elseif $alert->getSeverity() == 'warning'}exclamation-triangle text-warning
                                                      {elseif $alert->getSeverity() == 'info'}info-circle text-info
                                                      {else}check-circle text-success{/if} fa-lg"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        {$alert->getMessage()}
                                    </div>
                                    <div class="flex-shrink-0 ms-3">
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </div>
                            </a>
                        </li>
                    {foreachelse}
                        <li class="list-group-item border-0 text-center py-5">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="text-muted">{lang key='notificationsnone'}</p>
                        </li>
                    {/foreach}
                </ul>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{lang key='close'}</button>
            </div>
        </div>
    </div>
</div>

<style>

</style>

<script>
jQuery(document).ready(function($) {
    $('.mobile-nav-item').on('click', function() {
        $('.mobile-nav-item').removeClass('active');
        $(this).addClass('active');
    });
});
</script>