<?xml version="1.0" encoding="UTF-8"?>
<svg width="600px" height="500px" viewBox="0 0 600 500" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Hero Server</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#4A338D" offset="0%"></stop>
            <stop stop-color="#CC00BB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#4A338D" offset="0%"></stop>
            <stop stop-color="#CC00BB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Hero-Server" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Server Rack -->
        <g id="Server-Rack" transform="translate(150.000, 50.000)">
            <rect id="Rack-Base" fill="#E4E7EB" x="0" y="0" width="300" height="400" rx="10"></rect>
            
            <!-- Server Units -->
            <g id="Server-1" transform="translate(20.000, 30.000)">
                <rect id="Server-Base" fill="#FFFFFF" x="0" y="0" width="260" height="50" rx="5"></rect>
                <circle id="LED-1" fill="#4CAF50" cx="15" cy="25" r="5"></circle>
                <rect id="Handle-1" fill="#F5F7FA" x="30" y="15" width="30" height="20" rx="3"></rect>
                <rect id="Display-1" fill="#F5F7FA" x="70" y="15" width="80" height="20" rx="3"></rect>
                <rect id="Ports-1" fill="#F5F7FA" x="160" y="10" width="80" height="30" rx="3"></rect>
            </g>
            
            <g id="Server-2" transform="translate(20.000, 100.000)">
                <rect id="Server-Base" fill="#FFFFFF" x="0" y="0" width="260" height="50" rx="5"></rect>
                <circle id="LED-2" fill="#4CAF50" cx="15" cy="25" r="5"></circle>
                <rect id="Handle-2" fill="#F5F7FA" x="30" y="15" width="30" height="20" rx="3"></rect>
                <rect id="Display-2" fill="#F5F7FA" x="70" y="15" width="80" height="20" rx="3"></rect>
                <rect id="Ports-2" fill="#F5F7FA" x="160" y="10" width="80" height="30" rx="3"></rect>
            </g>
            
            <g id="Server-3" transform="translate(20.000, 170.000)">
                <rect id="Server-Base" fill="#FFFFFF" x="0" y="0" width="260" height="50" rx="5"></rect>
                <circle id="LED-3" fill="#FFC107" cx="15" cy="25" r="5"></circle>
                <rect id="Handle-3" fill="#F5F7FA" x="30" y="15" width="30" height="20" rx="3"></rect>
                <rect id="Display-3" fill="#F5F7FA" x="70" y="15" width="80" height="20" rx="3"></rect>
                <rect id="Ports-3" fill="#F5F7FA" x="160" y="10" width="80" height="30" rx="3"></rect>
            </g>
            
            <g id="Server-4" transform="translate(20.000, 240.000)">
                <rect id="Server-Base" fill="#FFFFFF" x="0" y="0" width="260" height="50" rx="5"></rect>
                <circle id="LED-4" fill="#4CAF50" cx="15" cy="25" r="5"></circle>
                <rect id="Handle-4" fill="#F5F7FA" x="30" y="15" width="30" height="20" rx="3"></rect>
                <rect id="Display-4" fill="#F5F7FA" x="70" y="15" width="80" height="20" rx="3"></rect>
                <rect id="Ports-4" fill="#F5F7FA" x="160" y="10" width="80" height="30" rx="3"></rect>
            </g>
            
            <g id="Server-5" transform="translate(20.000, 310.000)">
                <rect id="Server-Base" fill="#FFFFFF" x="0" y="0" width="260" height="50" rx="5"></rect>
                <circle id="LED-5" fill="#4CAF50" cx="15" cy="25" r="5"></circle>
                <rect id="Handle-5" fill="#F5F7FA" x="30" y="15" width="30" height="20" rx="3"></rect>
                <rect id="Display-5" fill="#F5F7FA" x="70" y="15" width="80" height="20" rx="3"></rect>
                <rect id="Ports-5" fill="#F5F7FA" x="160" y="10" width="80" height="30" rx="3"></rect>
            </g>
        </g>
        
        <!-- Connection Lines -->
        <path d="M300,80 C400,100 450,150 500,200" id="Connection-1" stroke="url(#linearGradient-2)" stroke-width="2" opacity="0.6"></path>
        <path d="M300,150 C400,170 450,220 500,270" id="Connection-2" stroke="url(#linearGradient-2)" stroke-width="2" opacity="0.6"></path>
        <path d="M300,220 C400,240 450,290 500,340" id="Connection-3" stroke="url(#linearGradient-2)" stroke-width="2" opacity="0.6"></path>
        
        <!-- Cloud Icons -->
        <g id="Cloud-1" transform="translate(500.000, 200.000)">
            <circle id="Cloud-Base" fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
            <path d="M40,25 C48,25 56,29 60,37 C68,37 76,45 76,53 C76,61 68,69 60,69 L20,69 C12,69 4,61 4,53 C4,45 12,37 20,37 C24,29 32,25 40,25 Z" id="Cloud-Shape" fill="url(#linearGradient-1)" opacity="0.8"></path>
        </g>
        
        <g id="Cloud-2" transform="translate(500.000, 270.000)">
            <circle id="Cloud-Base" fill="#FFFFFF" cx="30" cy="30" r="30"></circle>
            <path d="M30,18 C36,18 42,21 45,27 C51,27 57,33 57,39 C57,45 51,51 45,51 L15,51 C9,51 3,45 3,39 C3,33 9,27 15,27 C18,21 24,18 30,18 Z" id="Cloud-Shape" fill="url(#linearGradient-1)" opacity="0.6"></path>
        </g>
        
        <g id="Cloud-3" transform="translate(500.000, 340.000)">
            <circle id="Cloud-Base" fill="#FFFFFF" cx="35" cy="35" r="35"></circle>
            <path d="M35,22 C42,22 49,25 52.5,31.5 C59.5,31.5 66.5,38.5 66.5,45.5 C66.5,52.5 59.5,59.5 52.5,59.5 L17.5,59.5 C10.5,59.5 3.5,52.5 3.5,45.5 C3.5,38.5 10.5,31.5 17.5,31.5 C21,25 28,22 35,22 Z" id="Cloud-Shape" fill="url(#linearGradient-1)" opacity="0.7"></path>
        </g>
        
        <!-- Decorative Elements -->
        <circle id="Circle-1" fill="url(#linearGradient-1)" opacity="0.1" cx="100" cy="100" r="40"></circle>
        <circle id="Circle-2" fill="url(#linearGradient-1)" opacity="0.1" cx="500" cy="100" r="50"></circle>
        <circle id="Circle-3" fill="url(#linearGradient-1)" opacity="0.1" cx="100" cy="400" r="30"></circle>
        
        <!-- Data Flow Animation -->
        <circle id="Data-1" fill="#4A338D" cx="320" cy="80" r="3">
            <animate attributeName="cx" from="300" to="500" dur="3s" begin="0s" repeatCount="indefinite" />
            <animate attributeName="cy" from="80" to="200" dur="3s" begin="0s" repeatCount="indefinite" />
        </circle>
        
        <circle id="Data-2" fill="#4A338D" cx="320" cy="150" r="3">
            <animate attributeName="cx" from="300" to="500" dur="3s" begin="1s" repeatCount="indefinite" />
            <animate attributeName="cy" from="150" to="270" dur="3s" begin="1s" repeatCount="indefinite" />
        </circle>
        
        <circle id="Data-3" fill="#4A338D" cx="320" cy="220" r="3">
            <animate attributeName="cx" from="300" to="500" dur="3s" begin="2s" repeatCount="indefinite" />
            <animate attributeName="cy" from="220" to="340" dur="3s" begin="2s" repeatCount="indefinite" />
        </circle>
    </g>
</svg>
