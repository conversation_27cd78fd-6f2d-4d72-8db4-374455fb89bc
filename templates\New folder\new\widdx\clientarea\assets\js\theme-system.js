/**
 * WHMCS WIDDX Theme System
 * Unified Dark/Light Mode Functionality
 */

document.addEventListener('DOMContentLoaded', function() {
  'use strict';

  console.log('Theme System Initialized');

  // Get DOM elements
  var html = document.documentElement;
  var body = document.body;
  var toggleButton = document.querySelector('.ww-theme-toggle');
  var lightIcon = document.querySelector('.ww-theme-light');
  var darkIcon = document.querySelector('.ww-theme-dark');

  // Function to set theme
  function setTheme(theme) {
    // Set theme attribute on HTML and body
    html.setAttribute('data-bs-theme', theme);
    body.setAttribute('data-bs-theme', theme);

    // Store theme preference in localStorage
    localStorage.setItem('theme', theme);

    console.log('Theme set to:', theme);

    // Update tooltip text based on language direction
    if (lightIcon && darkIcon) {
      var isRTL = html.getAttribute('dir') === 'rtl';

      if (theme === 'dark') {
        if (isRTL) {
          // RTL language tooltip text
          lightIcon.setAttribute('data-title', 'فاتح');
          darkIcon.setAttribute('data-title', 'داكن');
        } else {
          // LTR language tooltip text
          lightIcon.setAttribute('data-title', 'Light');
          darkIcon.setAttribute('data-title', 'Dark');
        }
      } else {
        if (isRTL) {
          // RTL language tooltip text
          lightIcon.setAttribute('data-title', 'داكن');
          darkIcon.setAttribute('data-title', 'فاتح');
        } else {
          // LTR language tooltip text
          lightIcon.setAttribute('data-title', 'Dark');
          darkIcon.setAttribute('data-title', 'Light');
        }
      }
    }

    // Dispatch a custom event for other scripts to listen to
    var event = new CustomEvent('themeChanged', {
      detail: { theme: theme }
    });
    document.dispatchEvent(event);
  }

  // Function to toggle theme
  function toggleTheme() {
    var currentTheme = html.getAttribute('data-bs-theme') || 'light';
    var newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  }

  // Initialize theme from localStorage or system preference
  function initTheme() {
    var savedTheme = localStorage.getItem('theme');

    if (!savedTheme) {
      // Check system preference
      var prefersDarkMode = window.matchMedia &&
                           window.matchMedia('(prefers-color-scheme: dark)').matches;
      savedTheme = prefersDarkMode ? 'dark' : 'light';
    }

    setTheme(savedTheme);
  }

  // Add click event listener to toggle button
  if (toggleButton) {
    toggleButton.addEventListener('click', function(e) {
      e.preventDefault();
      toggleTheme();
    });
    console.log('Theme toggle button event listener added');
  } else {
    console.error('Theme toggle button not found');
  }

  // Listen for system theme changes
  if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
      if (!localStorage.getItem('theme')) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  // Initialize theme
  initTheme();
});
