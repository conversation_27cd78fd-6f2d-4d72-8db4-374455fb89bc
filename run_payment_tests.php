<?php
/**
 * Lahza Payment Gateway Test Runner
 * 
 * Executes comprehensive payment testing scenarios and generates detailed reports
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Set error reporting for testing
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Include required files
require_once __DIR__ . '/lahza_payment_test_suite.php';

class PaymentTestRunner {
    
    private $testSuite;
    private $logFile;
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->logFile = __DIR__ . '/test_execution_' . date('Ymd_His') . '.log';
        
        // Initialize test suite
        $this->testSuite = new LahzaPaymentTestSuite();
        
        $this->log("Test execution started");
    }
    
    /**
     * Run all payment tests with comprehensive reporting
     */
    public function executeTests() {
        $this->log("Starting comprehensive payment gateway tests");
        
        try {
            // Pre-test validation
            $this->validateTestEnvironment();
            
            // Execute test suite
            $this->testSuite->runAllTests();
            
            // Post-test analysis
            $this->analyzeResults();
            
            $this->log("Test execution completed successfully");
            
        } catch (Exception $e) {
            $this->log("Test execution failed: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Validate test environment before running tests
     */
    private function validateTestEnvironment() {
        echo "🔍 Validating Test Environment\n";
        echo str_repeat("-", 40) . "\n";
        
        // Check required files
        $requiredFiles = [
            __DIR__ . '/modules/gateways/lahza.php',
            __DIR__ . '/tests/lahza/test_config.php'
        ];
        
        foreach ($requiredFiles as $file) {
            if (!file_exists($file)) {
                throw new Exception("Required file not found: {$file}");
            }
            echo "✅ Found: " . basename($file) . "\n";
        }
        
        // Check test configuration
        $config = require __DIR__ . '/tests/lahza/test_config.php';
        
        if (empty($config['test_public_key']) || empty($config['test_secret_key'])) {
            throw new Exception("Test API keys not configured");
        }
        echo "✅ API keys configured\n";
        
        // Check network connectivity
        if (!$this->checkNetworkConnectivity($config['base_url'])) {
            throw new Exception("Cannot connect to Lahza API");
        }
        echo "✅ Network connectivity verified\n";
        
        echo "✅ Environment validation passed\n\n";
    }
    
    /**
     * Check network connectivity to Lahza API
     */
    private function checkNetworkConnectivity($url) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode > 0; // Any HTTP response indicates connectivity
    }
    
    /**
     * Analyze test results and generate insights
     */
    private function analyzeResults() {
        echo "\n🔬 Analyzing Test Results\n";
        echo str_repeat("-", 40) . "\n";
        
        // Load latest test report
        $reportFiles = glob(__DIR__ . '/lahza_test_report_*.json');
        if (empty($reportFiles)) {
            echo "⚠️ No test report found for analysis\n";
            return;
        }
        
        $latestReport = max($reportFiles);
        $reportData = json_decode(file_get_contents($latestReport), true);
        
        if (!$reportData) {
            echo "⚠️ Could not parse test report\n";
            return;
        }
        
        $this->generateInsights($reportData);
        $this->generateRecommendations($reportData);
    }
    
    /**
     * Generate test insights
     */
    private function generateInsights($reportData) {
        $summary = $reportData['summary'];
        $results = $reportData['test_results'];
        
        echo "📊 Test Insights:\n";
        
        // Performance analysis
        $durations = array_column($results, 'duration');
        $avgDuration = array_sum($durations) / count($durations);
        $maxDuration = max($durations);
        $minDuration = min($durations);
        
        echo "⏱️ Performance:\n";
        echo "   - Average test time: " . round($avgDuration, 2) . "ms\n";
        echo "   - Fastest test: " . round($minDuration, 2) . "ms\n";
        echo "   - Slowest test: " . round($maxDuration, 2) . "ms\n";
        
        // Failure analysis
        $failedTests = array_filter($results, function($test) {
            return !$test['success'];
        });
        
        if (!empty($failedTests)) {
            echo "\n❌ Failure Analysis:\n";
            $failureReasons = [];
            foreach ($failedTests as $test) {
                if ($test['error']) {
                    $failureReasons[] = $test['error'];
                }
            }
            
            $uniqueReasons = array_unique($failureReasons);
            foreach ($uniqueReasons as $reason) {
                $count = count(array_filter($failureReasons, function($r) use ($reason) {
                    return $r === $reason;
                }));
                echo "   - {$reason}: {$count} occurrence(s)\n";
            }
        }
        
        // Success rate by category
        $categories = [
            'Successful Payment' => 0,
            'Payment Failure' => 0,
            'Currency' => 0,
            '3D Secure' => 0,
            'Edge Cases' => 0
        ];
        
        foreach ($results as $test) {
            $testName = $test['name'];
            if (strpos($testName, 'Successful') !== false || strpos($testName, 'Visa Payment') !== false) {
                $categories['Successful Payment']++;
            } elseif (strpos($testName, 'Declined') !== false || strpos($testName, 'Insufficient') !== false) {
                $categories['Payment Failure']++;
            } elseif (strpos($testName, 'Currency') !== false) {
                $categories['Currency']++;
            } elseif (strpos($testName, '3DS') !== false || strpos($testName, '3D Secure') !== false) {
                $categories['3D Secure']++;
            } else {
                $categories['Edge Cases']++;
            }
        }
        
        echo "\n📈 Test Coverage:\n";
        foreach ($categories as $category => $count) {
            if ($count > 0) {
                echo "   - {$category}: {$count} tests\n";
            }
        }
    }
    
    /**
     * Generate recommendations based on test results
     */
    private function generateRecommendations($reportData) {
        $summary = $reportData['summary'];
        $results = $reportData['test_results'];
        
        echo "\n💡 Recommendations:\n";
        
        // Success rate recommendations
        if ($summary['success_rate'] < 95) {
            echo "⚠️ Success rate is below 95% - investigate failing tests\n";
        } elseif ($summary['success_rate'] >= 98) {
            echo "✅ Excellent success rate - gateway is performing well\n";
        }
        
        // Performance recommendations
        $durations = array_column($results, 'duration');
        $avgDuration = array_sum($durations) / count($durations);
        
        if ($avgDuration > 2000) {
            echo "⚠️ Average test time exceeds 2 seconds - consider performance optimization\n";
        } elseif ($avgDuration < 500) {
            echo "✅ Excellent performance - tests complete quickly\n";
        }
        
        // Specific recommendations
        $failedTests = array_filter($results, function($test) {
            return !$test['success'];
        });
        
        if (count($failedTests) > 0) {
            echo "🔧 Priority Actions:\n";
            foreach ($failedTests as $test) {
                echo "   - Fix: {$test['name']}\n";
                if ($test['error']) {
                    echo "     Error: {$test['error']}\n";
                }
            }
        }
        
        echo "\n📋 Next Steps:\n";
        echo "   1. Review failed tests and fix underlying issues\n";
        echo "   2. Run tests again to verify fixes\n";
        echo "   3. Consider adding more edge case tests\n";
        echo "   4. Set up automated testing in CI/CD pipeline\n";
    }
    
    /**
     * Log message with timestamp
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] [{$level}] {$message}\n";
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND);
        
        if ($level === 'ERROR') {
            error_log($logEntry);
        }
    }
}

// Execute tests if run directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $runner = new PaymentTestRunner();
        $runner->executeTests();
        
        echo "\n🎉 All tests completed successfully!\n";
        echo "Check the generated reports for detailed results.\n";
        
    } catch (Exception $e) {
        echo "\n💥 Test execution failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
