$(document).ready(function () {
    // Toggle the collapsible card body
    $(".card-header .card-minimise").on("click", function () {
      var $cardBody = $(this).closest(".card").find(".collapsable-card-body");
      var $icon = $(this);

      // Toggle the display of the collapsible card body
      $cardBody.slideToggle(200); // يمكنك تعديل السرعة حسب الحاجة

      // Toggle the icon state
      $icon.toggleClass("rotate");
    });

    // Ensure all collapsible card bodies are visible initially
    $(".collapsable-card-body").show();

    // Toggle the menu-sub visibility
    $(".menu-toggle").on("click", function (e) {
      e.preventDefault(); // Prevent default link behavior

      var $submenu = $(this).siblings(".menu-sub");

      // Check if submenu exists
      if ($submenu.length) {
        $submenu.slideToggle(); // Toggle submenu visibility
        $(".menu-sub").not($submenu).slideUp(); // Close other submenus
      } else {
        console.warn("Submenu not found");
      }
    });

    // Close submenus when clicking outside of them
    $(document).on("click", function (e) {
      if (!$(e.target).closest(".menu-item").length) {
        $(".menu-sub").slideUp(); // Hide all submenus
      }
    });

    // Copy referral link to clipboard and show modal
    $(".copy-button").on("click", function () {
      var copyText = $("#referralLink")[0];
      copyText.select();
      copyText.setSelectionRange(0, 99999); // For mobile devices

      navigator.clipboard.writeText(copyText.value).then(
        function () {
          $("#copyModal").modal("show");
        },
        function (err) {
          alert("Failed to copy the referral link. Please try again.");
        }
      );
    });

    // Ensure to hide the backdrop when the modal is closed
    $("#copyModal").on("hidden.bs.modal", function () {
      $(".modal-backdrop").remove();
    });

    // Search Modal Functionality
    $("#main-nav-search-btn").on("click", function () {
      $("#search-modal").addClass("active");
      $('#search-modal input[name="search"]').focus();
    });

    $("#search-close-btn").on("click", function () {
      $("#search-modal").removeClass("active");
    });

    $(document).on("keydown", function (event) {
      if (event.key === "Escape") {
        $("#search-modal").removeClass("active");
      }
    });

    // Sidebar Functionality
    $('[data-toggle="collapse"]').on("click", function () {
      $("#widdxSidebar").collapse("toggle");
    });

    $(".widdxsidebar .close").on("click", function () {
      $("#widdxSidebar").collapse("hide");
    });

    // Dark/Light Mode Toggle
    function setDarkMode(active) {
      var root = $(document.documentElement);
      var body = $(document.body);

      root.attr("data-bs-theme", active ? "dark" : "light");
      body.attr("data-bs-theme", active ? "dark" : "light");

      localStorage.setItem("theme", active ? "dark" : "light");

      // Update tooltip text
      var lightIcon = $(".ww-theme-light");
      var darkIcon = $(".ww-theme-dark");

      if (lightIcon.length && darkIcon.length) {
        if (active) {
          lightIcon.attr("data-title", "Light");
          darkIcon.attr("data-title", "Dark");
        } else {
          lightIcon.attr("data-title", "Dark");
          darkIcon.attr("data-title", "Light");
        }
      }
    }

    function toggleDarkMode() {
      var currentTheme = $(document.documentElement).attr("data-bs-theme");
      setDarkMode(currentTheme === "light");
    }

    function initDarkMode() {
      var savedTheme = localStorage.getItem("theme") || "light";
      setDarkMode(savedTheme === "dark");

      var toggleButton = $(".ww-theme-toggle");
      if (toggleButton.length) {
        toggleButton.on("click", function(e) {
          e.preventDefault();
          toggleDarkMode();
        });
      }
    }

    // Initialize dark mode when DOM is loaded
    initDarkMode();

    // Toggle password visibility
    $("#toggleIcon").on("click", function () {
      var $passwordInput = $("#password");
      var $toggleIcon = $(this);
      if ($passwordInput.attr("type") === "password") {
        $passwordInput.attr("type", "text");
        $toggleIcon.removeClass("fa-eye").addClass("fa-eye-slash");
      } else {
        $passwordInput.attr("type", "password");
        $toggleIcon.removeClass("fa-eye-slash").addClass("fa-eye");
      }
    });

    // Load image with fallback formats
    function loadImageWithFallbacks($imgElement, baseUrl, imageName) {
      var extensions = ["png", "jpg", "jpeg", "svg"]; // قائمة الصيغ التي نبحث عنها
      var loaded = false; // علامة تدل على ما إذا كانت الصورة قد تم تحميلها

      function tryNextExtension(index) {
        if (index >= extensions.length) {
          $imgElement.attr("src", baseUrl + "default.png"); // صورة افتراضية
          return;
        }
        var extension = extensions[index];
        $imgElement.attr("src", baseUrl + imageName + "." + extension);
        $imgElement
          .on("load", function () {
            loaded = true; // تم تحميل الصورة بنجاح
          })
          .on("error", function () {
            if (!loaded) {
              tryNextExtension(index + 1); // حاول الصيغة التالية
            }
          });
      }

      tryNextExtension(0); // ابدأ من الصيغة الأولى
    }

    // Highlight active mobile nav item
    $(".mobile-nav-item").on("click", function () {
      $(".mobile-nav-item").removeClass("active");
      $(this).addClass("active");
    });

    // Menu toggle functionality
    var $menuToggle = $('.layout-menu-toggle');
    var $layout = $('.layout-wrapper');

    if ($menuToggle.length && $layout.length) {
      $menuToggle.on('click', function(e) {
        e.preventDefault();
        console.log('Menu toggle clicked');
        $layout.toggleClass('layout-menu-expanded');
        console.log('layout-menu-expanded toggled');
      });
    } else {
      console.log('Menu toggle or layout element not found');
    }




    jQuery(document).ready(function($) {
      const searchModal = $('#search-modal');
      const searchInput = $('.search-input');
      const searchCloseBtn = $('#search-close-btn');

      function openSearchModal() {
          searchModal.addClass('active');
          setTimeout(() => searchInput.focus(), 300);
      }

      function closeSearchModal() {
          searchModal.removeClass('active');
      }

      // Event listener for closing the modal
      searchCloseBtn.on('click', closeSearchModal);

      // Close modal on Esc key press
      $(document).on('keydown', function(e) {
          if (e.key === "Escape") closeSearchModal();
      });

      // Prevent clicks inside the modal from closing it
      $('.search-content-area').on('click', function(e) {
          e.stopPropagation();
      });

      // Close modal when clicking outside the search area
      searchModal.on('click', closeSearchModal);

      // You'll need to add this event listener to your search trigger button
      // $('#search-trigger').on('click', openSearchModal);
  });

  });



