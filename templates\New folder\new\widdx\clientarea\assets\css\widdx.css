/* CSS لتحسين استجابة الجداول */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table thead th {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 1;
}

/* تحسينات على التنسيق للأجهزة الصغيرة */
@media (max-width: 768px) {
  .table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  .table thead {
    display: none;
  }
  .table tbody tr {
    display: block;
    margin-bottom: 1rem;
  }
  .table tbody td {
    display: block;
    text-align: right;
    position: relative;
    padding: 0.5rem;
    border-bottom: 1px solid #ddd;
  }
  .table tbody td::before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 45%;
    padding-right: 0.5rem;
    white-space: nowrap;
    font-weight: bold;
  }
}

.card-equal-height {
  height: 100%;
  transition: all 0.3s ease;
}
.card-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.3s ease;
}
.card-icon {
  transition: color 0.3s ease;
}

/* Hover effects */
.card-border-success:hover {
  background-color: #28a745; /* green */
  color: #ffffff !important; /* white text */
}
.card-border-success:hover .card-icon,
.card-border-success:hover .card-title {
  color: #ffffff !important; /* white icon and title */
}

.card-border-info:hover {
  background-color: #17a2b8; /* blue */
  color: #ffffff !important; /* white text */
}
.card-border-info:hover .card-icon,
.card-border-info:hover .card-title {
  color: #ffffff !important; /* white icon and title */
}

.card-border-warning:hover {
  background-color: #ffc107; /* yellow */
  color: #000000 !important; /* black text */
}
.card-border-warning:hover .card-icon,
.card-border-warning:hover .card-title {
  color: #000000 !important; /* black icon and title */
}

.card-border-primary:hover {
  background-color: #007bff; /* blue */
  color: #ffffff !important; /* white text */
}
.card-border-primary:hover .card-icon,
.card-border-primary:hover .card-title {
  color: #ffffff !important; /* white icon and title */
}

.card-border-danger:hover {
  background-color: #dc3545; /* red */
  color: #ffffff !important; /* white text */
}
.card-border-danger:hover .card-icon,
.card-border-danger:hover .card-title {
  color: #ffffff !important; /* white icon and title */
}

.copy-button {
  cursor: pointer;
  background-color: #007bff; /* Blue background */
  color: #ffffff; /* White text */
  border: none;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.3s ease;
}

.copy-button:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

.modal-body {
  text-align: center;
}

.checkmark-icon {
  font-size: 3rem;
  color: #28a745; /* Green color */
}

#chart {
  height: 300px; /* Adjust the height as needed */
}
@media (max-width: 768px) {
  #chart {
    height: 200px; /* Adjust for smaller screens */
  }
}

@media (min-width: 768px) {
  .hide-on-pc {
    display: none !important;
  }
}
@media (max-width: 767.98px) {
  .hide-on-mobile {
    display: none !important;
  }
  .layout-navbar.navbar-detached {
    width: 100% !important;
    position: fixed;
    top: -11px;
    box-shadow: 0px 0px 9px 0px #10101040 !important;
  }

  /* Styles for positioning the badge inside the icon */
  .mobile-nav-item {
    text-align: center;
    color: #4a338d;
    text-decoration: none;
    /*flex: 1;*/
    position: relative;
  }

  .mobile-nav-item i {
    font-size: 18px;
    color: #4a338d;
    position: relative;
  }

  .badge-notification {
    position: absolute;
    top: -4px;
    right: -2px;
    transform: translate(25%, -25%);
    background-color: red;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 50%;
    min-width: 15px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Additional styles for the mobile navigation */
  .mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;
    background-color: #fff;
    border-top: 1px solid #ddd;
    box-shadow: 0px 0px 9px 0px #10101040 !important;
    padding: 14px 0;
    z-index: 1000;
  }

  .mobile-nav-text {
    display: block;
    font-size: 12px;
    margin-top: 5px;
    color: #4a338d;
  }

  .modal-content {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
  }

  .notif-scroll {
    max-height: 400px;
    overflow-y: auto;
  }

  .notif-center {
    padding: 10px;
  }

  @media (max-width: 767.98px) {
    .sidebar {
      margin-top: 15%;
    }

    .sidebar.sidebar-secondary {
      margin-top: 5%;
    }
  }
}

@media (max-width: 1199.98px) {
  .layout-menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 250px;
    height: 100vh;
    z-index: 9999;
    transition: left 0.3s ease;
  }

  .layout-menu-expanded .layout-menu {
    left: 0;
  }

  .layout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }

  .layout-menu-expanded .layout-overlay {
    display: block;
  }
}

@media (min-width: 1200px) {
  .layout-menu-expanded .layout-menu {
    width: 250px;
  }

  .layout-menu-expanded .layout-page {
    margin-left: 250px;
  }
}

#seoResults .card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#seoResults .card-title {
  font-weight: 600;
  font-size: 1.25rem;
  color: #007bff;
}

#seoResults .list-group-item {
  background-color: #f8f9fa;
  border: 0;
  padding: 10px 20px;
}

#seoResults .card-body p {
  margin-bottom: 0;
  font-size: 1.1em;
  color: #333;
}

.input-group .form-control {
  border-radius: 0.25rem;
}

.input-group .input-group-append .btn {
  border-radius: 0.25rem;
}

#fullpage-overlay {
  transition: opacity 10s ease; /* تغيير الوقت حسب الحاجة */
}

#search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

#search-modal.active {
  opacity: 1;
  visibility: visible;
}

.search-content-area {
  width: 90%;
  max-width: 700px;
  position: relative;
  transform: translateY(-50px);
  transition: transform 0.3s ease;
}

#search-modal.active .search-content-area {
  transform: translateY(0);
}

.search-close {
  position: absolute;
  top: -50px;
  right: 0;
  color: #333;
  font-size: 28px;
  cursor: pointer;
  transition: transform 0.3s ease, color 0.3s ease;
}

.search-close:hover {
  transform: rotate(90deg);
  color: #666;
}

.search-form {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 20px 60px 20px 30px;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 50px;
  color: #333;
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  border-color: #999;
  outline: none;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.search-input::placeholder {
  color: #999;
}

.search-submit {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  font-size: 24px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-submit:hover {
  color: #333;
}

.search-hint {
  text-align: center;
  color: #666;
  margin-top: 20px;
  font-size: 16px;
}

.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  z-index: 1000;
}

.mobile-nav-item {
  color: #495057;
  transition: all 0.3s ease;
}

.mobile-nav-item:hover,
.mobile-nav-item:focus {
  color: #007bff;
  text-decoration: none;
}

.mobile-nav-item i {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.mobile-nav-item:hover i,
.mobile-nav-item:focus i {
  transform: translateY(-3px);
}

.mobile-nav-item small {
  font-size: 0.75rem;
  font-weight: 500;
}

.badge {
  transition: all 0.3s ease;
}

.mobile-nav-item:hover .badge,
.mobile-nav-item:focus .badge {
  transform: scale(1.1);
}

#notifModal .modal-content {
  border-radius: 15px;
  overflow: hidden;
}

#notifModal .list-group-item {
  transition: background-color 0.3s ease;
}

#notifModal .list-group-item:hover {
  background-color: #f8f9fa;
}

#notifModal .list-group-item i {
  transition: transform 0.3s ease;
}

#notifModal .list-group-item:hover i {
  transform: scale(1.1);
}

@media (max-width: 767.98px) {
  body {
    padding-bottom: 70px;
  }
}

@media (min-width: 768px) {
  .btn-return-to-admin {
    position: fixed !important;
    top: 425px;
    right: 0;
    padding: 5px 15px !important;
    background-color: rgb(215, 215, 215) !important;
    color: #777 !important;
    font-size: 0.9rem !important;
    border-radius: 0 0 3px 3px !important;
    text-decoration: none;
    transform: rotate(90deg);
    transform-origin: top right;
    z-index: 500;
  }
  .btn-return-to-admin:hover {
    background-color: rgb(205, 205, 205) !important;
    color: #444 !important;
    text-decoration: none;
  }
}
.btn-return-to-admin .floating {
  position: fixed !important;
  top: 425px;
  right: 0;
  padding: 5px 15px !important;
  background-color: rgb(215, 215, 215) !important;
  color: #777 !important;
  font-size: 0.9rem !important;
  border-radius: 0 0 3px 3px !important;
  text-decoration: none;
  transform: rotate(90deg);
  transform-origin: top right;
  z-index: 500;
}
.btn-return-to-admin .floating:hover {
  background-color: rgb(205, 205, 205) !important;
  color: #444 !important;
  text-decoration: none;
}
.promotions-slider-control .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%231c3965' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E");
}
.promotions-slider-control .carousel-control-next-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%231c3965' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E");
}
