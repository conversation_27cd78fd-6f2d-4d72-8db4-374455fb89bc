<!-- Notifications Widget -->
<li class="nav-item dropdown">
    <button class="nav-link nav-btn dropdown-toggle notification-toggle"
            type="button"
            id="notifDropdown"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
            aria-label="{lang key='notifications.toggle'} ({if count($clientAlerts) > 0}{count($clientAlerts)} {lang key='notifications.new'}{else}{lang key='notifications.none'}{/if})"
            title="{lang key='notifications.title'}">
        <div class="nav-icon-circle">
            <i class="fas fa-bell" aria-hidden="true"></i>
            {if count($clientAlerts) > 0}
                <span class="badge bg-danger badge-number notification-badge"
                      aria-label="{count($clientAlerts)} {lang key='notifications.new'}">
                    {count($clientAlerts)}
                </span>
            {/if}
        </div>
    </button>

    <ul class="dropdown-menu dropdown-menu-end notification-dropdown"
        aria-labelledby="notifDropdown">
        <li>
            <div class="dropdown-header notification-header">
                <h6 class="mb-0">
                    {if count($clientAlerts) > 0}
                        {lang key='notifications.youHave'} {count($clientAlerts)} {lang key='notifications.new'}
                    {else}
                        {lang key='notifications.noNew'}
                    {/if}
                </h6>
            </div>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
            <div class="notif-scroll">
                <div class="notif-center">
                    {foreach $clientAlerts as $alert}
                        <a class="dropdown-item notification-item"
                           href="{$alert->getLink()}"
                           role="menuitem">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-{if $alert->getSeverity() == 'danger'}exclamation-circle text-danger{elseif $alert->getSeverity() == 'warning'}exclamation-triangle text-warning{elseif $alert->getSeverity() == 'info'}info-circle text-info{else}check-circle text-success{/if}"
                                       aria-hidden="true"></i>
                                </div>
                                <div class="notification-content">
                                    <p class="mb-1 notification-message">{$alert->getMessage()}</p>
                                    <small class="text-muted notification-time">
                                        {lang key='notifications.recent'}
                                    </small>
                                </div>
                            </div>
                        </a>
                    {foreachelse}
                        <div class="dropdown-item text-center notification-empty">
                            <div class="py-3">
                                <i class="fas fa-bell-slash fa-2x text-muted mb-2" aria-hidden="true"></i>
                                <p class="mb-0 text-muted">{lang key='notifications.none'}</p>
                            </div>
                        </div>
                    {/foreach}
                </div>
            </div>
        </li>
        {if count($clientAlerts) > 0}
        <li><hr class="dropdown-divider"></li>
        <li>
            <a class="dropdown-item text-center notification-view-all"
               href="{$WEB_ROOT}/clientarea.php?action=emails"
               role="menuitem">
                <small>{lang key='notifications.viewAll'}</small>
            </a>
        </li>
        {/if}
    </ul>
</li>

<style>
/* Notification Widget Styles */
.notification-toggle {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--bs-body-bg);
  animation: notificationPulse 2s infinite;
}

@keyframes notificationPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-dropdown {
  min-width: 320px;
  max-width: 400px;
  border: 1px solid rgba(var(--bs-border-color-rgb), 0.15);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.notification-header {
  padding: 1rem 1.25rem 0.5rem;
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 0.5rem 0.5rem 0 0;
}

.notification-header h6 {
  color: var(--bs-primary);
  font-weight: 600;
}

.notif-scroll {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--bs-primary-rgb), 0.3) transparent;
}

.notif-scroll::-webkit-scrollbar {
  width: 6px;
}

.notif-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.notif-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(var(--bs-primary-rgb), 0.3);
  border-radius: 3px;
}

.notification-item {
  padding: 0.75rem 1.25rem;
  border: none;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.notification-icon {
  flex-shrink: 0;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--bs-body-color);
  margin: 0;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--bs-secondary-color);
}

.notification-empty {
  padding: 2rem 1.25rem;
}

.notification-view-all {
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 0 0 0.5rem 0.5rem;
}

.notification-view-all:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .notification-dropdown {
  background-color: var(--bs-dark);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .notification-header {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

[data-bs-theme="dark"] .notification-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .notification-view-all {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

[data-bs-theme="dark"] .notification-view-all:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.15);
}

[data-bs-theme="dark"] .notification-badge {
  border-color: var(--bs-dark);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .notification-badge {
    animation: none;
  }
}

.notification-toggle:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
  border-radius: 50%;
}
</style>
