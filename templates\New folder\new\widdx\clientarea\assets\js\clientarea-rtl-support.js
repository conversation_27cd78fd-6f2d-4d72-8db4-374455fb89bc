/**
 * WHMCS WIDDX RTL Support for Client Area Backend
 * Handles RTL-specific functionality for client area backend
 */

document.addEventListener('DOMContentLoaded', function() {
  'use strict';

  console.log('Client Area Backend RTL Support Initialized');

  // Check if the page is in RTL mode
  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';

  if (isRTL) {
    console.log('RTL mode detected in client area backend');

    // Add RTL class to body for global styling
    document.body.classList.add('rtl-mode');

    // Fix layout container for RTL
    fixLayoutForRTL();

    // Fix menu for RTL
    fixMenuForRTL();

    // Fix navbar for RTL
    fixNavbarForRTL();

    // Fix cards for RTL
    fixCardsForRTL();

    // Fix tables for RTL
    fixTablesForRTL();

    // Fix forms for RTL
    fixFormsForRTL();

    // Fix icons for RTL
    fixIconsForRTL();

    // Fix client area backend specific elements
    fixbackendBackendElements();

    // Listen for dynamic content changes
    setupMutationObserver();

    // Fix any RTL-specific layout issues on window resize
    setupResizeHandler();
  }

  /**
   * Fix layout for RTL
   */
  function fixLayoutForRTL() {
    // Fix layout container
    var layoutContainer = document.querySelector('.layout-container');
    if (layoutContainer) {
      layoutContainer.style.flexDirection = 'row-reverse';
    }

    // Fix layout page
    var layoutPage = document.querySelector('.layout-page');
    if (layoutPage) {
      layoutPage.style.paddingRight = '16.25rem';
      layoutPage.style.paddingLeft = '0';
    }

    // Fix content wrapper
    var contentWrapper = document.querySelector('.content-wrapper');
    if (contentWrapper) {
      contentWrapper.style.direction = 'rtl';
    }
  }

  /**
   * Fix menu for RTL
   */
  function fixMenuForRTL() {
    // Fix menu position
    var layoutMenu = document.querySelector('.layout-menu');
    if (layoutMenu) {
      layoutMenu.style.right = '0';
      layoutMenu.style.left = 'auto';
      layoutMenu.style.borderRight = '0';
      layoutMenu.style.borderLeft = '1px solid var(--border-color)';
    }

    // Fix menu items
    var menuItems = document.querySelectorAll('.menu-inner .menu-item .menu-link');
    menuItems.forEach(function(item) {
      item.style.textAlign = 'right';

      // Fix menu icons
      var menuIcon = item.querySelector('.menu-icon');
      if (menuIcon) {
        menuIcon.style.marginRight = '0';
        menuIcon.style.marginLeft = '1rem';
      }
    });

    // Fix menu toggles
    var menuToggles = document.querySelectorAll('.menu-toggle');
    menuToggles.forEach(function(toggle) {
      toggle.classList.add('rtl-toggle');
    });

    // Fix menu sub items
    var menuSubs = document.querySelectorAll('.menu-sub');
    menuSubs.forEach(function(sub) {
      sub.style.paddingRight = '1.5rem';
      sub.style.paddingLeft = '0';
    });
  }

  /**
   * Fix navbar for RTL
   */
  function fixNavbarForRTL() {
    // Fix navbar container
    var navbarContainer = document.querySelector('.layout-navbar .container-xxl');
    if (navbarContainer) {
      navbarContainer.style.flexDirection = 'row-reverse';
    }

    // Fix navbar brand
    var navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand) {
      navbarBrand.style.marginRight = '0';
      navbarBrand.style.marginLeft = '1rem';
    }

    // Fix navbar nav right
    var navbarNavRight = document.querySelector('.navbar-nav-right');
    if (navbarNavRight) {
      navbarNavRight.style.marginLeft = '0';
      navbarNavRight.style.marginRight = 'auto';
    }

    // Fix navbar search
    var navbarSearch = document.querySelector('.navbar-search-wrapper');
    if (navbarSearch) {
      navbarSearch.style.marginLeft = '0';
      navbarSearch.style.marginRight = 'auto';
    }

    // Fix navbar search input
    var searchInput = document.querySelector('.navbar-search .form-control');
    if (searchInput) {
      searchInput.style.paddingRight = '2.5rem';
      searchInput.style.paddingLeft = '0.75rem';
      searchInput.style.textAlign = 'right';
    }

    // Fix navbar search icon
    var searchIcon = document.querySelector('.navbar-search .search-icon');
    if (searchIcon) {
      searchIcon.style.right = '0.75rem';
      searchIcon.style.left = 'auto';
    }
  }

  /**
   * Fix cards for RTL
   */
  function fixCardsForRTL() {
    // Fix card headers
    var cardHeaders = document.querySelectorAll('.card-header');
    cardHeaders.forEach(function(header) {
      header.style.display = 'flex';
      header.style.flexDirection = 'row-reverse';
      header.style.justifyContent = 'space-between';
      header.style.direction = 'ltr';
    });

    // Fix card titles
    var cardTitles = document.querySelectorAll('.card-title');
    cardTitles.forEach(function(title) {
      title.style.marginRight = '0';
      title.style.marginLeft = 'auto';
    });

    // Fix card minimise buttons
    var cardMinimise = document.querySelectorAll('.card-minimise');
    cardMinimise.forEach(function(minimise) {
      minimise.style.marginLeft = '0';
      minimise.style.marginRight = 'auto';
    });

    // Fix tiles with statistics
    var tiles = document.querySelectorAll('.tiles .tile');
    tiles.forEach(function(tile) {
      tile.classList.add('rtl-tile');

      // Apply specific styling for statistics tiles
      var statValue = tile.querySelector('.stat-value');
      if (statValue) {
        statValue.style.marginTop = '20px';
        statValue.style.fontSize = '40px';
        statValue.style.lineHeight = '1';
        statValue.style.direction = 'ltr';
      }
    });
  }

  /**
   * Fix tables for RTL
   */
  function fixTablesForRTL() {
    // Fix table cells
    var tableCells = document.querySelectorAll('.table th, .table td');
    tableCells.forEach(function(cell) {
      cell.style.textAlign = 'right';
    });

    // Fix DataTables
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
      // Set default options for all DataTables
      jQuery.extend(true, jQuery.fn.DataTable.defaults, {
        language: {
          dir: 'rtl'
        }
      });

      // Fix existing DataTables
      jQuery('.dataTable').each(function() {
        var table = jQuery(this).DataTable();
        if (table) {
          // Redraw the table to apply RTL changes
          table.draw();
        }
      });
    }
  }

  /**
   * Fix forms for RTL
   */
  function fixFormsForRTL() {
    // Fix form labels
    var formLabels = document.querySelectorAll('.form-label');
    formLabels.forEach(function(label) {
      label.style.textAlign = 'right';
    });

    // Fix form checks
    var formChecks = document.querySelectorAll('.form-check');
    formChecks.forEach(function(check) {
      check.style.paddingLeft = '0';
      check.style.paddingRight = '1.5rem';
    });

    // Fix form check inputs
    var formCheckInputs = document.querySelectorAll('.form-check-input');
    formCheckInputs.forEach(function(input) {
      input.style.float = 'right';
      input.style.marginLeft = '0';
      input.style.marginRight = '-1.5rem';
    });

    // Fix form controls
    var formControls = document.querySelectorAll('.form-control');
    formControls.forEach(function(control) {
      control.style.textAlign = 'right';
      control.style.direction = 'rtl';
    });

    // Fix form selects
    var formSelects = document.querySelectorAll('.form-select');
    formSelects.forEach(function(select) {
      select.style.textAlign = 'right';
      select.style.paddingRight = '0.75rem';
      select.style.paddingLeft = '2.25rem';
      select.style.backgroundPosition = 'left 0.75rem center';
    });
  }

  /**
   * Fix icons for RTL
   */
  function fixIconsForRTL() {
    // Fix chevron icons
    var rightIcons = document.querySelectorAll('.bx-chevron-right, .fa-chevron-right');
    rightIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-right')) {
        icon.classList.remove('bx-chevron-right');
        icon.classList.add('bx-chevron-left');
      } else if (icon.classList.contains('fa-chevron-right')) {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-left');
      }
    });

    var leftIcons = document.querySelectorAll('.bx-chevron-left, .fa-chevron-left');
    leftIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-left')) {
        icon.classList.remove('bx-chevron-left');
        icon.classList.add('bx-chevron-right');
      } else if (icon.classList.contains('fa-chevron-left')) {
        icon.classList.remove('fa-chevron-left');
        icon.classList.add('fa-chevron-right');
      }
    });
  }

  /**
   * Fix client area backend specific elements
   */
  function fixbackendBackendElements() {
    // Add any client area backend specific fixes here
  }

  /**
   * Setup mutation observer to handle dynamically added content
   */
  function setupMutationObserver() {
    var observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.addedNodes.length) {
          // Check for newly added elements that need RTL fixes
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              // Apply RTL fixes to the new node
              applyRTLFixesToNode(node);
            }
          });
        }
      });
    });

    // Observe the entire document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Apply RTL fixes to a specific node
   */
  function applyRTLFixesToNode(node) {
    // Fix menu toggles
    var newToggles = node.querySelectorAll ? node.querySelectorAll('.menu-toggle') : [];
    newToggles.forEach(function(toggle) {
      toggle.classList.add('rtl-toggle');
    });

    // Fix dropdowns
    var newDropdowns = node.querySelectorAll ? node.querySelectorAll('.dropdown-menu') : [];
    newDropdowns.forEach(function(dropdown) {
      dropdown.classList.add('dropdown-menu-end');
    });

    // Fix input groups
    var newInputGroups = node.querySelectorAll ? node.querySelectorAll('.input-group') : [];
    newInputGroups.forEach(function(group) {
      group.classList.add('rtl-input-group');
    });

    // Fix icons
    var newRightIcons = node.querySelectorAll ? node.querySelectorAll('.bx-chevron-right, .fa-chevron-right') : [];
    newRightIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-right')) {
        icon.classList.remove('bx-chevron-right');
        icon.classList.add('bx-chevron-left');
      } else if (icon.classList.contains('fa-chevron-right')) {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-left');
      }
    });

    var newLeftIcons = node.querySelectorAll ? node.querySelectorAll('.bx-chevron-left, .fa-chevron-left') : [];
    newLeftIcons.forEach(function(icon) {
      if (icon.classList.contains('bx-chevron-left')) {
        icon.classList.remove('bx-chevron-left');
        icon.classList.add('bx-chevron-right');
      } else if (icon.classList.contains('fa-chevron-left')) {
        icon.classList.remove('fa-chevron-left');
        icon.classList.add('fa-chevron-right');
      }
    });

    // Fix card headers
    var newCardHeaders = node.querySelectorAll ? node.querySelectorAll('.card-header') : [];
    newCardHeaders.forEach(function(header) {
      header.style.display = 'flex';
      header.style.flexDirection = 'row-reverse';
      header.style.justifyContent = 'space-between';
      header.style.direction = 'ltr';
    });

    // Fix tiles with statistics
    var newTiles = node.querySelectorAll ? node.querySelectorAll('.tiles .tile') : [];
    newTiles.forEach(function(tile) {
      tile.classList.add('rtl-tile');

      // Apply specific styling for statistics tiles
      var statValue = tile.querySelector('.stat-value');
      if (statValue) {
        statValue.style.marginTop = '20px';
        statValue.style.fontSize = '40px';
        statValue.style.lineHeight = '1';
        statValue.style.direction = 'ltr';
      }
    });
  }

  /**
   * Setup resize handler for responsive RTL fixes
   */
  function setupResizeHandler() {
    window.addEventListener('resize', function() {
      // Fix responsive elements on resize
      if (window.innerWidth <= 991.98) {
        // Mobile layout fixes for RTL
        var layoutMenu = document.querySelector('.layout-menu');
        if (layoutMenu) {
          layoutMenu.style.transform = 'translateX(100%)';
        }

        // Fix mobile navbar brand in RTL
        var navbarBrand = document.querySelector('.navbar-brand.mx-auto');
        if (navbarBrand) {
          navbarBrand.style.transform = 'translateX(50%)';
        }

        // Fix mobile menu toggle in RTL
        var menuToggle = document.querySelector('.layout-menu-toggle');
        if (menuToggle) {
          menuToggle.style.marginRight = '0';
          menuToggle.style.marginLeft = 'auto';
        }
      }
    });
  }
});
