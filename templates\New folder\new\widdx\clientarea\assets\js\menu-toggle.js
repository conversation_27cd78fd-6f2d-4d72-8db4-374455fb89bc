/**
 * WIDDX Menu Toggle Functionality
 * Enhanced menu system with smooth animations and RTL support
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initMenuToggle();
    });

    /**
     * Initialize menu toggle functionality
     */
    function initMenuToggle() {
        const menuToggles = document.querySelectorAll('.menu-toggle');
        
        menuToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const menuItem = this.closest('.menu-item');
                const submenu = menuItem.querySelector('.menu-sub');
                
                if (!submenu) return;
                
                // Toggle current menu
                toggleSubmenu(menuItem, submenu);
                
                // Close other open menus (optional - for accordion behavior)
                closeOtherMenus(menuItem);
            });
        });

        // Close menus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.layout-menu')) {
                closeAllMenus();
            }
        });

        // Handle keyboard navigation
        handleKeyboardNavigation();
    }

    /**
     * Toggle submenu visibility
     */
    function toggleSubmenu(menuItem, submenu) {
        const isOpen = menuItem.classList.contains('open');
        const toggle = menuItem.querySelector('.menu-toggle');
        
        if (isOpen) {
            // Close menu
            menuItem.classList.remove('open');
            toggle.setAttribute('aria-expanded', 'false');
            
            // Animate close
            submenu.style.maxHeight = submenu.scrollHeight + 'px';
            requestAnimationFrame(function() {
                submenu.style.maxHeight = '0px';
                submenu.style.opacity = '0';
            });
            
            setTimeout(function() {
                submenu.style.display = 'none';
            }, 300);
            
        } else {
            // Open menu
            menuItem.classList.add('open');
            toggle.setAttribute('aria-expanded', 'true');
            
            // Animate open
            submenu.style.display = 'block';
            submenu.style.maxHeight = '0px';
            submenu.style.opacity = '0';
            
            requestAnimationFrame(function() {
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                submenu.style.opacity = '1';
            });
            
            setTimeout(function() {
                submenu.style.maxHeight = 'none';
            }, 300);
        }
    }

    /**
     * Close other open menus (accordion behavior)
     */
    function closeOtherMenus(currentMenuItem) {
        const allMenuItems = document.querySelectorAll('.menu-item.open');
        
        allMenuItems.forEach(function(menuItem) {
            if (menuItem !== currentMenuItem) {
                const submenu = menuItem.querySelector('.menu-sub');
                const toggle = menuItem.querySelector('.menu-toggle');
                
                if (submenu && toggle) {
                    menuItem.classList.remove('open');
                    toggle.setAttribute('aria-expanded', 'false');
                    
                    submenu.style.maxHeight = '0px';
                    submenu.style.opacity = '0';
                    
                    setTimeout(function() {
                        submenu.style.display = 'none';
                    }, 300);
                }
            }
        });
    }

    /**
     * Close all open menus
     */
    function closeAllMenus() {
        const openMenuItems = document.querySelectorAll('.menu-item.open');
        
        openMenuItems.forEach(function(menuItem) {
            const submenu = menuItem.querySelector('.menu-sub');
            const toggle = menuItem.querySelector('.menu-toggle');
            
            if (submenu && toggle) {
                menuItem.classList.remove('open');
                toggle.setAttribute('aria-expanded', 'false');
                
                submenu.style.maxHeight = '0px';
                submenu.style.opacity = '0';
                
                setTimeout(function() {
                    submenu.style.display = 'none';
                }, 300);
            }
        });
    }

    /**
     * Handle keyboard navigation
     */
    function handleKeyboardNavigation() {
        const menuLinks = document.querySelectorAll('.menu-link');
        
        menuLinks.forEach(function(link) {
            link.addEventListener('keydown', function(e) {
                const menuItem = this.closest('.menu-item');
                
                switch(e.key) {
                    case 'Enter':
                    case ' ':
                        if (this.classList.contains('menu-toggle')) {
                            e.preventDefault();
                            this.click();
                        }
                        break;
                        
                    case 'Escape':
                        closeAllMenus();
                        this.blur();
                        break;
                        
                    case 'ArrowDown':
                        e.preventDefault();
                        focusNextMenuItem(menuItem);
                        break;
                        
                    case 'ArrowUp':
                        e.preventDefault();
                        focusPreviousMenuItem(menuItem);
                        break;
                        
                    case 'ArrowRight':
                        if (!document.documentElement.dir === 'rtl') {
                            if (this.classList.contains('menu-toggle') && !menuItem.classList.contains('open')) {
                                this.click();
                            }
                        }
                        break;
                        
                    case 'ArrowLeft':
                        if (document.documentElement.dir === 'rtl') {
                            if (this.classList.contains('menu-toggle') && !menuItem.classList.contains('open')) {
                                this.click();
                            }
                        } else {
                            if (menuItem.classList.contains('open')) {
                                this.click();
                            }
                        }
                        break;
                }
            });
        });
    }

    /**
     * Focus next menu item
     */
    function focusNextMenuItem(currentItem) {
        const allItems = Array.from(document.querySelectorAll('.menu-item'));
        const currentIndex = allItems.indexOf(currentItem);
        const nextItem = allItems[currentIndex + 1];
        
        if (nextItem) {
            const nextLink = nextItem.querySelector('.menu-link');
            if (nextLink) {
                nextLink.focus();
            }
        }
    }

    /**
     * Focus previous menu item
     */
    function focusPreviousMenuItem(currentItem) {
        const allItems = Array.from(document.querySelectorAll('.menu-item'));
        const currentIndex = allItems.indexOf(currentItem);
        const previousItem = allItems[currentIndex - 1];
        
        if (previousItem) {
            const previousLink = previousItem.querySelector('.menu-link');
            if (previousLink) {
                previousLink.focus();
            }
        }
    }

    /**
     * Initialize menu state from localStorage (optional)
     */
    function initMenuState() {
        try {
            const savedState = localStorage.getItem('widdx-menu-state');
            if (savedState) {
                const openMenus = JSON.parse(savedState);
                openMenus.forEach(function(menuId) {
                    const menuItem = document.getElementById(menuId);
                    if (menuItem && menuItem.querySelector('.menu-sub')) {
                        const submenu = menuItem.querySelector('.menu-sub');
                        const toggle = menuItem.querySelector('.menu-toggle');
                        
                        menuItem.classList.add('open');
                        if (toggle) {
                            toggle.setAttribute('aria-expanded', 'true');
                        }
                        
                        submenu.style.display = 'block';
                        submenu.style.opacity = '1';
                        submenu.style.maxHeight = 'none';
                    }
                });
            }
        } catch (e) {
            console.warn('Could not restore menu state:', e);
        }
    }

    /**
     * Save menu state to localStorage (optional)
     */
    function saveMenuState() {
        try {
            const openMenus = Array.from(document.querySelectorAll('.menu-item.open'))
                .map(function(item) { return item.id; })
                .filter(function(id) { return id; });
            
            localStorage.setItem('widdx-menu-state', JSON.stringify(openMenus));
        } catch (e) {
            console.warn('Could not save menu state:', e);
        }
    }

    // Save menu state before page unload (optional)
    window.addEventListener('beforeunload', saveMenuState);

    // Initialize menu state after DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initMenuState, 100);
    });

})();
