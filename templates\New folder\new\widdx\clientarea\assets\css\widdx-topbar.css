/*!
 * WIDDX Topbar Styles
 * Enhanced navigation bar with dark/light mode support
 * Copyright (c) 2024 WIDDX Template
 */

/* ===== TOPBAR BASE STYLES ===== */
.layout-navbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 0.5rem;
  margin-top: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* ===== MENU TOGGLE ===== */
.btn-menu-toggle {
  border: none;
  background: transparent;
  padding: 0;
}

.menu-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-toggle-icon:hover,
.btn-menu-toggle:focus .menu-toggle-icon {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
}

/* ===== LOGO STYLES ===== */
.logo-img {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.logo-img:hover {
  transform: scale(1.05);
}

/* ===== NAVIGATION BUTTONS ===== */
.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--bs-body-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  padding: 0;
  text-decoration: none;
  position: relative;
}

.nav-btn:hover, 
.nav-btn:focus {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  transform: scale(1.05);
  text-decoration: none;
}

.nav-btn:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

.nav-icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

/* ===== SEARCH STYLES ===== */
.search-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.search-toggle-btn:hover,
.search-toggle-btn:focus {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* ===== LANGUAGE SELECTOR ===== */
.lang-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  padding: 0;
  cursor: pointer;
}

.lang-selector:hover,
.lang-selector:focus {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* ===== BADGE STYLES ===== */
.badge-number {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 18px;
  height: 18px;
  font-size: 0.75rem;
  line-height: 1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== DARK MODE ADJUSTMENTS ===== */
[data-bs-theme="dark"] .layout-navbar {
  background-color: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(51, 65, 85, 0.3);
}

[data-bs-theme="dark"] .menu-toggle-icon,
[data-bs-theme="dark"] .search-toggle-btn,
[data-bs-theme="dark"] .lang-selector {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--bs-primary);
}

[data-bs-theme="dark"] .menu-toggle-icon:hover,
[data-bs-theme="dark"] .search-toggle-btn:hover,
[data-bs-theme="dark"] .lang-selector:hover,
[data-bs-theme="dark"] .btn-menu-toggle:focus .menu-toggle-icon {
  background-color: rgba(255, 255, 255, 0.15);
}

[data-bs-theme="dark"] .nav-btn {
  color: var(--bs-gray-300);
}

[data-bs-theme="dark"] .nav-btn:hover,
[data-bs-theme="dark"] .nav-btn:focus {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--bs-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1199.98px) {
  .layout-navbar {
    margin-top: 0.5rem;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
  }

  .navbar-brand {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .layout-menu-toggle {
    margin-right: auto;
    z-index: 2;
  }

  #navbar-collapse {
    justify-content: flex-end;
    z-index: 2;
  }

  .hide-on-mobile {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hide-on-pc {
    display: none !important;
  }
}

/* RTL Support */
[dir="rtl"] .navbar-brand {
  right: 50%;
  transform: translateX(50%);
}

[dir="rtl"] .layout-menu-toggle {
  margin-left: auto;
  margin-right: 0;
}

[dir="rtl"] #navbar-collapse {
  justify-content: flex-start;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .layout-navbar,
  .menu-toggle-icon,
  .logo-img,
  .nav-btn,
  .search-toggle-btn,
  .lang-selector {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .layout-navbar {
    border: 2px solid var(--bs-primary);
  }
  
  .nav-btn:focus,
  .search-toggle-btn:focus,
  .lang-selector:focus {
    outline: 3px solid var(--bs-primary);
    outline-offset: 3px;
  }
}
